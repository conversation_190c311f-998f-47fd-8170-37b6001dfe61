import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { GoodsReceiptsPageClient } from '@/components/goods-receipts/GoodsReceiptsPageClient';
import { GoodsReceiptStatsCards } from '@/components/goods-receipts/goods-receipt-stats-cards';
import { GoodsReceiptQueryParams } from '@/types/goods-receipt';
import { DEFAULT_GOODS_RECEIPT_PAGINATION } from '@/lib/constants/goods-receipt';

export const metadata: Metadata = {
  title: 'Penerimaan Barang - Apotek App',
  description: 'Kelola penerimaan barang dan kontrol kualitas',
};

interface GoodsReceiptsPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function GoodsReceiptsPage({ searchParams }: GoodsReceiptsPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  // Parse search parameters
  const resolvedSearchParams = await searchParams;

  const filters: GoodsReceiptQueryParams = {
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page as string) : DEFAULT_GOODS_RECEIPT_PAGINATION.page,
    limit: resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit as string) : DEFAULT_GOODS_RECEIPT_PAGINATION.limit,
    search: (resolvedSearchParams.search as string) || undefined,
    supplierId: (resolvedSearchParams.supplierId as string) || undefined,
    purchaseOrderId: (resolvedSearchParams.purchaseOrderId as string) || undefined,
    status: (resolvedSearchParams.status as any) || undefined,
    qualityStatus: (resolvedSearchParams.qualityStatus as any) || undefined,
    receiptDateFrom: (resolvedSearchParams.receiptDateFrom as string) || undefined,
    receiptDateTo: (resolvedSearchParams.receiptDateTo as string) || undefined,
    deliveryDateFrom: (resolvedSearchParams.deliveryDateFrom as string) || undefined,
    deliveryDateTo: (resolvedSearchParams.deliveryDateTo as string) || undefined,
    inspectionBy: (resolvedSearchParams.inspectionBy as string) || undefined,
    sortBy: (resolvedSearchParams.sortBy as string) || DEFAULT_GOODS_RECEIPT_PAGINATION.sortBy,
    sortOrder: (resolvedSearchParams.sortOrder as 'asc' | 'desc') || DEFAULT_GOODS_RECEIPT_PAGINATION.sortOrder,
  };

  // Extract period for statistics
  const statsPeriod = (resolvedSearchParams.period as string) || undefined;

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards */}
          <div className="px-4 lg:px-6">
            <GoodsReceiptStatsCards period={statsPeriod} />
          </div>

          {/* Client-side interactive components */}
          <div className="px-4 lg:px-6">
            <GoodsReceiptsPageClient
              initialQuery={filters}
            />
          </div>
        </div>
      </div>
    </>
  );
}
