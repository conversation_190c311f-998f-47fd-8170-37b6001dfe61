import { Select, SelectContent, SelectGroup, SelectI<PERSON>, Select<PERSON><PERSON>l, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ReferenceType } from "@/types/inventory";
import { REFERENCE_TYPE_OPTIONS } from "@/lib/constants/inventory";

interface ReferenceTypeSelectProps {
  value?: ReferenceType | string;
  onValueChange?: (value: ReferenceType | string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  includeEmpty?: boolean;
  emptyLabel?: string;
}

export function ReferenceTypeSelect({
  value,
  onValueChange,
  placeholder = "Pilih jenis referensi",
  disabled = false,
  className,
  includeEmpty = false,
  emptyLabel = "<PERSON><PERSON>a jenis referensi"
}: ReferenceTypeSelectProps) {
  // Group options by category
  const salesOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.SALE, ReferenceType.SALE_CANCELLATION, ReferenceType.SALE_REFUND, ReferenceType.SALE_DELETION].includes(opt.value)
  );

  const purchaseOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.PURCHASE, ReferenceType.PURCHASE_RETURN].includes(opt.value)
  );

  const inventoryOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.INITIAL_STOCK, ReferenceType.ADJUSTMENT, ReferenceType.TRANSFER].includes(opt.value)
  );

  const allocationOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.ALLOCATION, ReferenceType.DEALLOCATION].includes(opt.value)
  );

  const statusOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.ACTIVATION, ReferenceType.DEACTIVATION].includes(opt.value)
  );

  const customerOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.CUSTOMER_RESERVATION, ReferenceType.CUSTOMER_ORDER].includes(opt.value)
  );

  const expiryOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.EXPIRY_WRITE_OFF].includes(opt.value)
  );

  const otherOptions = REFERENCE_TYPE_OPTIONS.filter(opt =>
    [ReferenceType.OTHER].includes(opt.value)
  );

  return (
    <Select
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {includeEmpty && (
          <SelectItem value="ALL">
            {emptyLabel}
          </SelectItem>
        )}

        <SelectGroup>
          <SelectLabel>Penjualan</SelectLabel>
          {salesOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Pembelian</SelectLabel>
          {purchaseOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Inventaris</SelectLabel>
          {inventoryOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Alokasi</SelectLabel>
          {allocationOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Status</SelectLabel>
          {statusOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Pelanggan</SelectLabel>
          {customerOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Kedaluwarsa</SelectLabel>
          {expiryOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Lainnya</SelectLabel>
          {otherOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
} 