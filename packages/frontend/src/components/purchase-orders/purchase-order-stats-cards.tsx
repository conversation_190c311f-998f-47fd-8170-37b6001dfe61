'use client';

import {
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Package,
  AlertTriangle,
  Send,
  Users,
  Zap,
  BarChart3,
  Timer,
  Target,
  Activity,
  XCircle,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { usePurchaseOrderStats } from '@/hooks/usePurchaseOrders';
import { formatCurrency, formatNumber } from '@/lib/utils';

interface PurchaseOrderStatsCardsProps {
  period?: string;
}

export function PurchaseOrderStatsCards({ period }: PurchaseOrderStatsCardsProps = {}) {
  const { data: stats, isLoading, error } = usePurchaseOrderStats(period);

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <Card className="col-span-full">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <AlertTriangle className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground mb-1">Gagal memuat statistik purchase order</p>
            <p className="text-xs text-muted-foreground">
              {error instanceof Error ? error.message : 'Terjadi kesalahan yang tidak diketahui'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  // Ensure all required fields have safe defaults to prevent runtime errors
  const safeStats = {
    totalOrders: stats.totalOrders ?? 0,
    totalValue: stats.totalValue ?? 0,
    pendingValue: stats.pendingValue ?? 0,
    activeValue: stats.activeValue ?? 0,
    completedValue: stats.completedValue ?? 0,
    statusStats: stats.statusStats ?? {},
    valueStats: stats.valueStats ?? {},
    supplierStats: stats.supplierStats ?? [],
    processingTimeAnalytics: {
      averageProcessingTime: stats.processingTimeAnalytics?.averageProcessingTime ?? 0,
      totalOrders: stats.processingTimeAnalytics?.totalOrders ?? 0,
      efficiencyMetrics: {
        fastProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.fastProcessing ?? 0,
        slowProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.slowProcessing ?? 0,
        averageProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.averageProcessing ?? 0,
        fastPercentage: stats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0,
        slowPercentage: stats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0,
      },
    },
    volumeTrends: stats.volumeTrends ?? [],
  };

  // Calculate derived metrics
  const draft = safeStats.statusStats.DRAFT || 0;
  const submitted = safeStats.statusStats.SUBMITTED || 0;
  const approved = safeStats.statusStats.APPROVED || 0;
  const ordered = safeStats.statusStats.ORDERED || 0;
  const partiallyReceived = safeStats.statusStats.PARTIALLY_RECEIVED || 0;
  const completed = safeStats.statusStats.COMPLETED || 0;
  const cancelled = safeStats.statusStats.CANCELLED || 0;

  const pendingCount = draft + submitted;
  const activeCount = approved + ordered + partiallyReceived;
  const completionRate = safeStats.totalOrders > 0 ? (completed / safeStats.totalOrders) * 100 : 0;

  // Helper functions for analytics with additional safety checks
  const getVolumeTrend = () => {
    if (!safeStats.volumeTrends || safeStats.volumeTrends.length < 2) return null;
    const recent = safeStats.volumeTrends.slice(-2);
    const current = recent[1]?.count || 0;
    const previous = recent[0]?.count || 0;
    return current > previous ? 'up' : current < previous ? 'down' : 'stable';
  };

  const getTopSupplier = () => {
    if (!safeStats.supplierStats || safeStats.supplierStats.length === 0) return null;
    return safeStats.supplierStats[0]; // Already sorted by order count
  };

  const volumeTrend = getVolumeTrend();
  const topSupplier = getTopSupplier();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Purchase Orders with Volume Trend */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Purchase Order</CardTitle>
          <div className="flex items-center gap-1">
            {volumeTrend === 'up' && <TrendingUp className="h-3 w-3 text-green-600" />}
            {volumeTrend === 'down' && <TrendingDown className="h-3 w-3 text-red-600" />}
            <FileText className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeStats.totalOrders.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            {volumeTrend === 'up' && '↗ Meningkat dari periode sebelumnya'}
            {volumeTrend === 'down' && '↘ Menurun dari periode sebelumnya'}
            {(!volumeTrend || volumeTrend === 'stable') && 'Semua purchase order'}
          </p>
        </CardContent>
      </Card>

      {/* Total Value */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Nilai</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(safeStats.totalValue)}</div>
          <p className="text-xs text-muted-foreground">
            Nilai total purchase order
          </p>
        </CardContent>
      </Card>

      {/* Pending Orders with Alert */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Menunggu Proses</CardTitle>
          <div className="flex items-center gap-1">
            {pendingCount > 10 && <AlertTriangle className="h-3 w-3 text-orange-600" />}
            <Clock className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {pendingCount.toLocaleString()}
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Draft: {draft} • Diajukan: {submitted}
            </p>
            {pendingCount > 10 && (
              <Badge variant="outline" className="text-xs">
                Prioritas Tinggi
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Active Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Sedang Berjalan</CardTitle>
          <Send className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {activeCount.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Disetujui: {approved} • Dipesan: {ordered}
          </p>
        </CardContent>
      </Card>

      {/* Completion Rate with Performance Indicator */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tingkat Penyelesaian</CardTitle>
          <div className="flex items-center gap-1">
            {completionRate >= 80 && <Target className="h-3 w-3 text-green-600" />}
            {completionRate < 60 && <AlertTriangle className="h-3 w-3 text-red-600" />}
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${completionRate >= 80 ? 'text-green-600' :
            completionRate >= 60 ? 'text-blue-600' : 'text-red-600'
            }`}>
            {completionRate.toFixed(1)}%
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              {completed} dari {safeStats.totalOrders} selesai
            </p>
            {completionRate >= 80 && (
              <Badge variant="outline" className="text-xs text-green-600">
                Baik
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Processing Efficiency */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Efisiensi Proses</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0).toFixed(0)}%
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Proses cepat (&lt;24 jam)
            </p>
            {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0) >= 70 && (
              <Badge variant="outline" className="text-xs text-green-600">
                Efisien
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Average Processing Time */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Rata-rata Proses</CardTitle>
          <Timer className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {Math.round(safeStats.processingTimeAnalytics?.averageProcessingTime ?? 0)}h
          </div>
          <p className="text-xs text-muted-foreground">
            Waktu rata-rata pemrosesan
          </p>
        </CardContent>
      </Card>

      {/* Completed Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Selesai</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {completed.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Nilai: {formatCurrency(safeStats.completedValue)}
          </p>
        </CardContent>
      </Card>

      {/* Partially Received (if any) */}
      {partiallyReceived > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Diterima Sebagian</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {partiallyReceived.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Memerlukan tindak lanjut
            </p>
          </CardContent>
        </Card>
      )}

      {/* Cancelled Orders (if any) */}
      {cancelled > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dibatalkan</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {cancelled.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Purchase order dibatalkan
            </p>
          </CardContent>
        </Card>
      )}

      {/* Top Supplier (if available) */}
      {topSupplier && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Supplier Teratas</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-green-600 mb-1">
              {topSupplier.orderCount} order
            </div>
            <p className="text-xs text-muted-foreground truncate">
              {topSupplier.supplierName}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(topSupplier.totalValue)}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Volume Trends */}
      {safeStats.volumeTrends && safeStats.volumeTrends.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tren Volume</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {safeStats.volumeTrends.slice(-1)[0]?.count || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Order hari terakhir
            </p>
          </CardContent>
        </Card>
      )}

      {/* Slow Processing Alert (if significant) */}
      {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0) > 20 && (
        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Proses Lambat</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0).toFixed(0)}%
            </div>
            <div className="flex items-center gap-2">
              <p className="text-xs text-muted-foreground">
                Proses &gt;72 jam
              </p>
              <Badge variant="outline" className="text-xs text-orange-600">
                Perhatian
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pending Value Alert (if significant) */}
      {safeStats.pendingValue > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nilai Pending</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatCurrency(safeStats.pendingValue)}
            </div>
            <p className="text-xs text-muted-foreground">
              Menunggu persetujuan
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
