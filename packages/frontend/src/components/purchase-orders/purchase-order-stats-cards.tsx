'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  TrendingUp,
  Package,
  AlertTriangle,
  Send,
} from 'lucide-react';
import { usePurchaseOrderStats } from '@/hooks/usePurchaseOrders';
import { formatCurrency, formatNumber } from '@/lib/utils';

export function PurchaseOrderStatsCards() {
  const { data: apiResponse, isLoading, error } = usePurchaseOrderStats();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !apiResponse) {
    return (
      <div className="grid grid-cols-1 gap-4 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center h-20">
              <p className="text-sm text-muted-foreground">
                Gagal memuat statistik
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Transform API response to expected format with safe fallbacks
  const statusStats = apiResponse.statusStats || {};
  const stats = {
    total: apiResponse.totalOrders || 0,
    draft: statusStats.DRAFT || 0,
    submitted: statusStats.SUBMITTED || 0,
    approved: statusStats.APPROVED || 0,
    ordered: statusStats.ORDERED || 0,
    partiallyReceived: statusStats.PARTIALLY_RECEIVED || 0,
    completed: statusStats.COMPLETED || 0,
    cancelled: statusStats.CANCELLED || 0,
    totalValue: apiResponse.totalValue || 0,
    pendingValue: 0, // Not provided by API, using fallback
    completedValue: 0, // Not provided by API, using fallback
  };

  const pendingCount = stats.draft + stats.submitted;
  const activeCount = stats.approved + stats.ordered + stats.partiallyReceived;
  const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

  return (
    <div className="grid grid-cols-1 gap-4 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      {/* Total Purchase Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Purchase Order
          </CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total)}</div>
          <p className="text-xs text-muted-foreground">
            Semua purchase order
          </p>
        </CardContent>
      </Card>

      {/* Pending Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Menunggu Proses
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(pendingCount)}</div>
          <p className="text-xs text-muted-foreground">
            Draft: {stats.draft} • Diajukan: {stats.submitted}
          </p>
        </CardContent>
      </Card>

      {/* Active Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Sedang Berjalan
          </CardTitle>
          <Send className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(activeCount)}</div>
          <p className="text-xs text-muted-foreground">
            Disetujui: {stats.approved} • Dipesan: {stats.ordered}
          </p>
        </CardContent>
      </Card>

      {/* Total Value */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Nilai
          </CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
          <p className="text-xs text-muted-foreground">
            Pending: {formatCurrency(stats.pendingValue)}
          </p>
        </CardContent>
      </Card>

      {/* Completion Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Tingkat Penyelesaian
          </CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{completionRate.toFixed(1)}%</div>
          <p className="text-xs text-muted-foreground">
            {stats.completed} dari {stats.total} selesai
          </p>
        </CardContent>
      </Card>

      {/* Completed Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Selesai
          </CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.completed)}</div>
          <p className="text-xs text-muted-foreground">
            Nilai: {formatCurrency(stats.completedValue)}
          </p>
        </CardContent>
      </Card>

      {/* Partially Received */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Diterima Sebagian
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.partiallyReceived)}</div>
          <p className="text-xs text-muted-foreground">
            Memerlukan tindak lanjut
          </p>
        </CardContent>
      </Card>

      {/* Cancelled Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Dibatalkan
          </CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.cancelled)}</div>
          <p className="text-xs text-muted-foreground">
            Purchase order dibatalkan
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
