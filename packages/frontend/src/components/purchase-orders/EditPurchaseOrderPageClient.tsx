'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PurchaseOrderForm } from '@/components/purchase-orders/purchase-order-form';
import { usePurchaseOrder, useUpdatePurchaseOrder } from '@/hooks/usePurchaseOrders';
import { CreatePurchaseOrderDto, UpdatePurchaseOrderDto, PurchaseOrderFormData } from '@/types/purchase-order';
import { navigateBackWithFallback } from '@/lib/utils/navigation';

interface EditPurchaseOrderPageClientProps {
  purchaseOrderId: string;
}

export function EditPurchaseOrderPageClient({ purchaseOrderId }: EditPurchaseOrderPageClientProps) {
  const router = useRouter();
  const { data: purchaseOrder, isLoading, error } = usePurchaseOrder(purchaseOrderId);
  const updatePurchaseOrderMutation = useUpdatePurchaseOrder();

  const handleSubmit = async (data: CreatePurchaseOrderDto) => {
    try {
      // Convert CreatePurchaseOrderDto to UpdatePurchaseOrderDto
      const updateData: UpdatePurchaseOrderDto = {
        supplierId: data.supplierId,
        orderDate: data.orderDate,
        expectedDelivery: data.expectedDelivery,
        discountType: data.discountType,
        discountValue: data.discountValue,
        taxAmount: data.taxAmount,
        autoCalculateTax: data.autoCalculateTax,
        taxInclusive: data.taxInclusive,
        paymentTerms: data.paymentTerms,
        paymentMethod: data.paymentMethod,
        deliveryAddress: data.deliveryAddress,
        deliveryContact: data.deliveryContact,
        deliveryPhone: data.deliveryPhone,
        deliveryNotes: data.deliveryNotes,
        notes: data.notes,
        internalNotes: data.internalNotes,
        items: data.items,
      };

      await updatePurchaseOrderMutation.mutateAsync({
        id: purchaseOrderId,
        data: updateData,
      });
    } catch (error) {
      console.error('Failed to update purchase order:', error);
      // Error handling is done in the mutation hook
    }
  };

  const handleCancel = () => {
    navigateBackWithFallback(router, `/dashboard/purchase-orders/${purchaseOrderId}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-20" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>

        {/* Form Skeleton */}
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Purchase Order</h1>
            <p className="text-muted-foreground">
              Purchase order tidak ditemukan atau terjadi kesalahan
            </p>
          </div>
        </div>

        {/* Error Message */}
        <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-4">
          <p className="text-sm text-destructive">
            {error ? 'Gagal memuat data purchase order' : 'Purchase order tidak ditemukan'}
          </p>
        </div>
      </div>
    );
  }

  // Check if purchase order can be edited
  if (purchaseOrder.status !== 'DRAFT') {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Purchase Order</h1>
            <p className="text-muted-foreground">
              {purchaseOrder.orderNumber}
            </p>
          </div>
        </div>

        {/* Warning Message */}
        <div className="rounded-lg border border-yellow-500/50 bg-yellow-500/10 p-4">
          <p className="text-sm text-yellow-700">
            Purchase order dengan status "{purchaseOrder.status}" tidak dapat diedit.
            Hanya purchase order dengan status "DRAFT" yang dapat diedit.
          </p>
        </div>
      </div>
    );
  }

  // Convert purchase order to form data
  const initialData: Partial<PurchaseOrderFormData> = {
    supplierId: purchaseOrder.supplierId,
    orderDate: purchaseOrder.orderDate.split('T')[0], // Convert to date string
    expectedDelivery: purchaseOrder.expectedDelivery?.split('T')[0],
    discountType: purchaseOrder.discountType as any,
    discountValue: purchaseOrder.discountValue,
    paymentTerms: purchaseOrder.paymentTerms,
    paymentMethod: purchaseOrder.paymentMethod,
    deliveryAddress: purchaseOrder.deliveryAddress,
    deliveryContact: purchaseOrder.deliveryContact,
    deliveryPhone: purchaseOrder.deliveryPhone,
    deliveryNotes: purchaseOrder.deliveryNotes,
    notes: purchaseOrder.notes,
    internalNotes: purchaseOrder.internalNotes,
    // Include computed totals for display
    subtotal: purchaseOrder.subtotal,
    discountAmount: purchaseOrder.discountAmount,
    taxAmount: purchaseOrder.taxAmount,
    totalAmount: purchaseOrder.totalAmount,
    items: purchaseOrder.items.map(item => ({
      productId: item.productId,
      unitId: item.unitId,
      quantityOrdered: item.quantityOrdered,
      unitPrice: item.unitPrice,
      discountType: item.discountType as any,
      discountValue: item.discountValue,
      expectedDelivery: item.expectedDelivery?.split('T')[0],
      qualitySpecs: item.qualitySpecs,
      notes: item.notes,
    })),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          disabled={updatePurchaseOrderMutation.isPending}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Edit Purchase Order</h1>
          <p className="text-muted-foreground">
            {purchaseOrder.orderNumber}
          </p>
        </div>
      </div>

      {/* Form */}
      <PurchaseOrderForm
        initialData={initialData}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isSubmitting={updatePurchaseOrderMutation.isPending}
        submitLabel="Simpan Perubahan"
      />
    </div>
  );
}
