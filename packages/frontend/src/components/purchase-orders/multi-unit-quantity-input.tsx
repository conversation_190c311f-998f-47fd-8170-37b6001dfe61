'use client';

import * as React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { ProductUnitHierarchy } from '@/types/product';

interface MultiUnitQuantityInputProps {
  productId?: string;
  units: ProductUnitHierarchy[];
  selectedUnitId?: string;
  quantity?: number;
  onUnitChange: (unitId: string) => void;
  onQuantityChange: (quantity: number) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  showConversion?: boolean;
}

export function MultiUnitQuantityInput({
  productId,
  units,
  selectedUnitId,
  quantity = 0,
  onUnitChange,
  onQuantityChange,
  disabled = false,
  error,
  className,
  showConversion = true,
}: MultiUnitQuantityInputProps) {
  const selectedUnit = units.find(unit => unit.unitId === selectedUnitId);
  const baseUnit = units.find(unit => unit.level === 0);

  // Calculate conversion display
  const conversionDisplay = React.useMemo(() => {
    if (!showConversion || !selectedUnit || !baseUnit || selectedUnit.unitId === baseUnit.unitId || quantity === 0) {
      return null;
    }

    const baseQuantity = quantity * selectedUnit.conversionFactor;
    return `= ${baseQuantity} ${baseUnit.unit.abbreviation}`;
  }, [selectedUnit, baseUnit, quantity, showConversion]);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '') {
      onQuantityChange(0);
      return;
    }

    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 0) {
      onQuantityChange(numValue);
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      <div className="grid grid-cols-2 gap-2">
        {/* Quantity Input */}
        <div>
          <Input
            id={`quantity-${productId}`}
            type="number"
            min="0"
            step="1"
            value={quantity || ''}
            onChange={handleQuantityChange}
            disabled={disabled}
            className={cn(
              "h-8 text-sm",
              error && "border-destructive focus-visible:ring-destructive"
            )}
            placeholder="0"
          />
        </div>

        {/* Unit Selector */}
        <div>
          <Select
            value={selectedUnitId || 'NONE'}
            onValueChange={(value) => onUnitChange(value === 'NONE' ? '' : value)}
            disabled={disabled || units.length === 0}
          >
            <SelectTrigger size='sm'
              id={`unit-${productId}`}
              className={cn(
                error && "border-destructive focus-visible:ring-destructive"
              )}
            >
              <SelectValue placeholder="Pilih satuan" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="NONE">Pilih satuan</SelectItem>
              {units.map((unitHierarchy) => (
                <SelectItem key={unitHierarchy.unitId} value={unitHierarchy.unitId}>
                  <div className="flex items-center justify-between w-full">
                    <span>{unitHierarchy.unit.name}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      ({unitHierarchy.unit.abbreviation})
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Conversion Display */}
      {conversionDisplay && (
        <div className="text-sm text-muted-foreground">
          {quantity} {selectedUnit?.unit.abbreviation} {conversionDisplay}
        </div>
      )}

      {/* Unit Details */}
      {selectedUnit && selectedUnit.conversionFactor !== 1 && (
        <div className="text-xs text-muted-foreground">
          1 {selectedUnit.unit.abbreviation} = {selectedUnit.conversionFactor} {baseUnit?.unit.abbreviation}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="text-sm text-destructive" role="alert">
          {error}
        </p>
      )}
    </div>
  );
}
