'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PurchaseOrderForm } from '@/components/purchase-orders/purchase-order-form';
import { useCreatePurchaseOrder } from '@/hooks/usePurchaseOrders';
import { CreatePurchaseOrderDto } from '@/types/purchase-order';
import { navigateBackWithFallback, FALLBACK_ROUTES } from '@/lib/utils/navigation';

export function NewPurchaseOrderPageClient() {
  const router = useRouter();
  const createPurchaseOrderMutation = useCreatePurchaseOrder();

  const handleSubmit = async (data: CreatePurchaseOrderDto) => {
    try {
      const newPurchaseOrder = await createPurchaseOrderMutation.mutateAsync(data);
      // Navigate to the purchase order detail page
      router.push(`/dashboard/purchase-orders/${newPurchaseOrder.id}`);
    } catch (error) {
      console.error('Failed to create purchase order:', error);
      // Error handling is done in the mutation hook
    }
  };

  const handleCancel = () => {
    navigateBackWithFallback(router, '/dashboard/purchase-orders');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          disabled={createPurchaseOrderMutation.isPending}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Buat Purchase Order Baru</h1>
          <p className="text-muted-foreground">
            Buat purchase order baru untuk pemesanan ke supplier
          </p>
        </div>
      </div>

      {/* Form */}
      <PurchaseOrderForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isSubmitting={createPurchaseOrderMutation.isPending}
        submitLabel="Buat Purchase Order"
      />
    </div>
  );
}
