'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  ArrowLeft,
  Edit,
  CheckCircle,
  XCircle,
  Send,
  FileText,
  Printer,
  Package,
  Calendar,
  User,
  Building2,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Calculator,
  Clock,
  AlertTriangle,
} from 'lucide-react';
import { PurchaseOrder, PurchaseOrderStatus } from '@/types/purchase-order';
import { 
  getPurchaseOrderStatusLabel, 
  getPurchaseOrderStatusColor,
  getPaymentMethodLabel,
  getAllowedNextStatuses,
} from '@/lib/constants/purchase-order';
import { formatCurrency, formatDate, formatDateTime } from '@/lib/utils';

interface PurchaseOrderDetailProps {
  purchaseOrder: PurchaseOrder;
  onEdit: () => void;
  onApprove: (notes?: string) => void;
  onCancel: (reason: string) => void;
  onSend: () => void;
  onPrint: () => void;
  onBack: () => void;
  isLoading?: boolean;
}

export function PurchaseOrderDetail({
  purchaseOrder,
  onEdit,
  onApprove,
  onCancel,
  onSend,
  onPrint,
  onBack,
  isLoading = false,
}: PurchaseOrderDetailProps) {
  const [cancelReason, setCancelReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');

  const allowedNextStatuses = getAllowedNextStatuses(purchaseOrder.status);
  const canEdit = purchaseOrder.status === PurchaseOrderStatus.DRAFT;
  const canApprove = purchaseOrder.status === PurchaseOrderStatus.SUBMITTED;
  const canSend = purchaseOrder.status === PurchaseOrderStatus.APPROVED;
  const canCancel = [
    PurchaseOrderStatus.DRAFT,
    PurchaseOrderStatus.SUBMITTED,
    PurchaseOrderStatus.APPROVED,
    PurchaseOrderStatus.ORDERED,
  ].includes(purchaseOrder.status);

  const handleApprove = () => {
    onApprove(approvalNotes || undefined);
    setApprovalNotes('');
  };

  const handleCancel = () => {
    if (cancelReason.trim()) {
      onCancel(cancelReason);
      setCancelReason('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            disabled={isLoading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{purchaseOrder.orderNumber}</h1>
            <p className="text-muted-foreground">
              Purchase Order Detail
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge 
            variant="outline" 
            className={getPurchaseOrderStatusColor(purchaseOrder.status)}
          >
            {getPurchaseOrderStatusLabel(purchaseOrder.status)}
          </Badge>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onPrint}
              disabled={isLoading}
            >
              <Printer className="h-4 w-4 mr-2" />
              Cetak
            </Button>

            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                disabled={isLoading}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}

            {canApprove && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button size="sm" disabled={isLoading}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Setujui
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Setujui Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Apakah Anda yakin ingin menyetujui purchase order ini?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <div className="space-y-2">
                    <Label htmlFor="approval-notes">Catatan Persetujuan (Opsional)</Label>
                    <Textarea
                      id="approval-notes"
                      value={approvalNotes}
                      onChange={(e) => setApprovalNotes(e.target.value)}
                      placeholder="Tambahkan catatan persetujuan..."
                      rows={3}
                    />
                  </div>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction onClick={handleApprove}>
                      Setujui
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canSend && (
              <Button size="sm" onClick={onSend} disabled={isLoading}>
                <Send className="h-4 w-4 mr-2" />
                Kirim ke Supplier
              </Button>
            )}

            {canCancel && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm" disabled={isLoading}>
                    <XCircle className="h-4 w-4 mr-2" />
                    Batalkan
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Batalkan Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Apakah Anda yakin ingin membatalkan purchase order ini? Tindakan ini tidak dapat dibatalkan.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <div className="space-y-2">
                    <Label htmlFor="cancel-reason">Alasan Pembatalan *</Label>
                    <Textarea
                      id="cancel-reason"
                      value={cancelReason}
                      onChange={(e) => setCancelReason(e.target.value)}
                      placeholder="Masukkan alasan pembatalan..."
                      rows={3}
                      required
                    />
                  </div>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleCancel}
                      disabled={!cancelReason.trim()}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Batalkan Purchase Order
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Purchase Order Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informasi Purchase Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Nomor PO
                  </Label>
                  <p className="font-medium">{purchaseOrder.orderNumber}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Status
                  </Label>
                  <div className="mt-1">
                    <Badge 
                      variant="outline" 
                      className={getPurchaseOrderStatusColor(purchaseOrder.status)}
                    >
                      {getPurchaseOrderStatusLabel(purchaseOrder.status)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Tanggal Order
                  </Label>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(purchaseOrder.orderDate)}
                  </p>
                </div>
                {purchaseOrder.expectedDelivery && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Estimasi Pengiriman
                    </Label>
                    <p className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      {formatDate(purchaseOrder.expectedDelivery)}
                    </p>
                  </div>
                )}
                {purchaseOrder.paymentTerms && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Termin Pembayaran
                    </Label>
                    <p>{purchaseOrder.paymentTerms} hari</p>
                  </div>
                )}
                {purchaseOrder.paymentMethod && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Metode Pembayaran
                    </Label>
                    <p className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      {getPaymentMethodLabel(purchaseOrder.paymentMethod)}
                    </p>
                  </div>
                )}
              </div>

              {purchaseOrder.notes && (
                <>
                  <Separator />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Catatan
                    </Label>
                    <p className="mt-1 text-sm">{purchaseOrder.notes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Items Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Item Purchase Order
              </CardTitle>
              <CardDescription>
                {purchaseOrder.items.length} item dalam purchase order ini
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Produk</TableHead>
                      <TableHead>Kuantitas</TableHead>
                      <TableHead>Harga Satuan</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseOrder.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {item.product.code} • {item.product.category}
                            </div>
                            {item.product.manufacturer && (
                              <div className="text-xs text-muted-foreground">
                                {item.product.manufacturer}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {item.quantityOrdered} {item.unit.abbreviation}
                            </div>
                            {item.quantityReceived > 0 && (
                              <div className="text-sm text-muted-foreground">
                                Diterima: {item.quantityReceived}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(item.unitPrice)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {formatCurrency(item.totalPrice)}
                            </div>
                            {item.discountAmount > 0 && (
                              <div className="text-sm text-green-600">
                                Diskon: -{formatCurrency(item.discountAmount)}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {item.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Informasi Supplier
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Nama Supplier
                </Label>
                <p className="font-medium">{purchaseOrder.supplier.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Kode Supplier
                </Label>
                <p className="text-sm font-mono">{purchaseOrder.supplier.code}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Jenis
                </Label>
                <p className="text-sm">{purchaseOrder.supplier.type}</p>
              </div>
              {purchaseOrder.supplier.city && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Kota
                  </Label>
                  <p className="flex items-center gap-2 text-sm">
                    <MapPin className="h-3 w-3" />
                    {purchaseOrder.supplier.city}
                  </p>
                </div>
              )}
              {purchaseOrder.supplier.phone && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Telepon
                  </Label>
                  <p className="flex items-center gap-2 text-sm">
                    <Phone className="h-3 w-3" />
                    {purchaseOrder.supplier.phone}
                  </p>
                </div>
              )}
              {purchaseOrder.supplier.email && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Email
                  </Label>
                  <p className="flex items-center gap-2 text-sm">
                    <Mail className="h-3 w-3" />
                    {purchaseOrder.supplier.email}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Ringkasan Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span className="font-medium">{formatCurrency(purchaseOrder.subtotal)}</span>
              </div>
              
              {purchaseOrder.discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Diskon:</span>
                  <span>-{formatCurrency(purchaseOrder.discountAmount)}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>PPN:</span>
                <span>{formatCurrency(purchaseOrder.taxAmount)}</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between text-lg font-semibold">
                <span>Total:</span>
                <span>{formatCurrency(purchaseOrder.totalAmount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Audit Trail */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Riwayat
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Dibuat
                </Label>
                <p className="text-sm">
                  {formatDateTime(purchaseOrder.createdAt)}
                  {purchaseOrder.createdByUser && (
                    <span className="block text-muted-foreground">
                      oleh {purchaseOrder.createdByUser.name}
                    </span>
                  )}
                </p>
              </div>

              {purchaseOrder.submittedAt && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Diajukan
                  </Label>
                  <p className="text-sm">
                    {formatDateTime(purchaseOrder.submittedAt)}
                    {purchaseOrder.submittedByUser && (
                      <span className="block text-muted-foreground">
                        oleh {purchaseOrder.submittedByUser.name}
                      </span>
                    )}
                  </p>
                </div>
              )}

              {purchaseOrder.approvedAt && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Disetujui
                  </Label>
                  <p className="text-sm">
                    {formatDateTime(purchaseOrder.approvedAt)}
                    {purchaseOrder.approvedByUser && (
                      <span className="block text-muted-foreground">
                        oleh {purchaseOrder.approvedByUser.name}
                      </span>
                    )}
                  </p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Terakhir Diperbarui
                </Label>
                <p className="text-sm">
                  {formatDateTime(purchaseOrder.updatedAt)}
                  {purchaseOrder.updatedByUser && (
                    <span className="block text-muted-foreground">
                      oleh {purchaseOrder.updatedByUser.name}
                    </span>
                  )}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
