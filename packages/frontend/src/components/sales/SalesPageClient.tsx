'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/sales/data-table';
import { createSaleColumns } from '@/components/sales/columns';
import { SalesQuickView } from '@/components/sales/sales-quick-view';
import { ReceiptModal } from '@/components/receipt/receipt-modal';
import { Sale, SaleQueryParams } from '@/types/sales';
import {
  useSales,
  useSalesInvalidate,
  useCompleteSale,
  useCancelSale,
  useRefundSale,
  useGenerateReceipt,
  useGenerateReceiptWide,
  useExportSales,
  useBulkUpdateSaleStatus,
  useDeleteSale,
  useBulkDeleteSales,
} from '@/hooks/useSales';
import {
  DEFAULT_SALES_PAGINATION,
} from '@/lib/constants/sales';

interface SalesPageClientProps {
  initialQuery: SaleQueryParams;
}

export function SalesPageClient({
  initialQuery,
}: SalesPageClientProps) {
  const router = useRouter();

  // Query state for API calls
  const [query, setQuery] = useState<SaleQueryParams>(initialQuery);

  // Filter state for UI
  const [filters, setFilters] = useState<SaleQueryParams>(initialQuery);

  // Use TanStack Query for data fetching
  const { data, isLoading, error, refetch } = useSales(query);

  // Use invalidation hook for stats refresh
  const { invalidateStats } = useSalesInvalidate();

  // Use mutation hooks for sale operations
  const completeSaleMutation = useCompleteSale();
  const cancelSaleMutation = useCancelSale();
  const refundSaleMutation = useRefundSale();
  const generateReceiptMutation = useGenerateReceipt();
  const generateReceiptWideMutation = useGenerateReceiptWide();
  const exportSalesMutation = useExportSales();
  const bulkUpdateStatusMutation = useBulkUpdateSaleStatus();
  const deleteSaleMutation = useDeleteSale();
  const bulkDeleteSalesMutation = useBulkDeleteSales();

  // Quick view state
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [showQuickView, setShowQuickView] = useState(false);

  // Receipt modal state
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [selectedReceiptData, setSelectedReceiptData] = useState<any>(null);
  const [selectedSaleId, setSelectedSaleId] = useState<string | null>(null);

  const handleQueryChange = useCallback((newQuery: SaleQueryParams) => {
    setQuery(newQuery);
    setFilters(newQuery);
  }, []);

  const handleFilterChange = useCallback((key: keyof SaleQueryParams, value: any) => {
    // Don't reset page for pagination-related changes or sorting changes
    const isPaginationChange = key === 'page' || key === 'limit';
    const isSortingChange = key === 'sortBy' || key === 'sortOrder';

    const newFilters = {
      ...filters,
      [key]: value,
      // Only reset to page 1 for actual filter changes, not pagination or sorting changes
      ...(isPaginationChange || isSortingChange ? {} : { page: 1 }),
    };

    // Only update if the value actually changed
    if (filters[key] !== value) {
      setFilters(newFilters);
      handleQueryChange(newFilters);
    }
  }, [filters, handleQueryChange]);

  const handleBatchFilterChange = useCallback((updates: Partial<SaleQueryParams>) => {
    const newFilters = {
      ...filters,
      ...updates,
      page: 1, // Reset to first page when filtering
    };

    setFilters(newFilters);
    handleQueryChange(newFilters);
  }, [filters, handleQueryChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      page: 1,
      limit: query.limit || DEFAULT_SALES_PAGINATION.limit,
      sortBy: query.sortBy || DEFAULT_SALES_PAGINATION.sortBy,
      sortOrder: query.sortOrder || DEFAULT_SALES_PAGINATION.sortOrder,
      search: query.search, // Keep search term
    };
    setFilters(clearedFilters);
    handleQueryChange(clearedFilters);
  }, [query.limit, query.sortBy, query.sortOrder, query.search, handleQueryChange]);

  const handleViewSale = useCallback((sale: Sale) => {
    setSelectedSale(sale);
    setShowQuickView(true);
  }, []);

  const handleEditSale = useCallback((sale: Sale) => {
    router.push(`/dashboard/sales/${sale.id}/edit`);
  }, [router]);

  const handleCompleteSale = useCallback(async (sale: Sale) => {
    try {
      await completeSaleMutation.mutateAsync(sale.id);
      invalidateStats();
      setShowQuickView(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      // Just log for debugging purposes
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to complete sale:', error);
      }
    }
  }, [completeSaleMutation, invalidateStats]);

  const handleCancelSale = useCallback(async (sale: Sale) => {
    try {
      const reason = prompt('Alasan pembatalan (opsional):');
      await cancelSaleMutation.mutateAsync({ id: sale.id, reason: reason || undefined });
      invalidateStats();
      setShowQuickView(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to cancel sale:', error);
      }
    }
  }, [cancelSaleMutation, invalidateStats]);

  const handleRefundSale = useCallback(async (sale: Sale) => {
    try {
      const reason = prompt('Alasan refund (opsional):');
      await refundSaleMutation.mutateAsync({ id: sale.id, reason: reason || undefined });
      invalidateStats();
      setShowQuickView(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to refund sale:', error);
      }
    }
  }, [refundSaleMutation, invalidateStats]);

  const handleGenerateReceipt = useCallback(async (sale: Sale) => {
    try {
      const receipt = await generateReceiptMutation.mutateAsync(sale.id);
      setSelectedReceiptData(receipt);
      setSelectedSaleId(sale.id);
      setShowReceiptModal(true);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to generate receipt:', error);
      }
    }
  }, [generateReceiptMutation]);

  const handleGenerateReceiptWide = useCallback(async (sale: Sale) => {
    try {
      const receipt = await generateReceiptWideMutation.mutateAsync(sale.id);
      setSelectedReceiptData(receipt);
      setSelectedSaleId(sale.id);
      setShowReceiptModal(true);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to generate wide receipt:', error);
      }
    }
  }, [generateReceiptWideMutation]);

  const handleViewDetails = useCallback((sale: Sale) => {
    router.push(`/dashboard/sales/${sale.id}`);
  }, [router]);

  const handleRefresh = useCallback(() => {
    refetch();
    invalidateStats();
  }, [refetch, invalidateStats]);

  const handleExport = useCallback(async () => {
    try {
      const blob = await exportSalesMutation.mutateAsync({
        ...query,
        format: 'excel'
      });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `sales-export-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to export sales:', error);
    }
  }, [exportSalesMutation, query]);

  const handleCreateSale = useCallback(() => {
    router.push('/dashboard/sales/create');
  }, [router]);

  // Bulk operation handlers
  const handleBulkComplete = useCallback(async (saleIds: string[]) => {
    try {
      await bulkUpdateStatusMutation.mutateAsync({
        saleIds,
        status: 'COMPLETED',
      });
      refetch();
      invalidateStats();
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to complete sales:', error);
      }
    }
  }, [bulkUpdateStatusMutation, refetch, invalidateStats]);

  const handleBulkCancel = useCallback(async (saleIds: string[]) => {
    try {
      await bulkUpdateStatusMutation.mutateAsync({
        saleIds,
        status: 'CANCELLED',
        reason: 'Bulk cancellation',
      });
      refetch();
      invalidateStats();
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to cancel sales:', error);
      }
    }
  }, [bulkUpdateStatusMutation, refetch, invalidateStats]);

  const handleDeleteSale = useCallback(async (sale: Sale) => {
    try {
      await deleteSaleMutation.mutateAsync({ 
        id: sale.id,
        reason: `Deleted from sales page`
      });
      invalidateStats();
      setShowQuickView(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to delete sale:', error);
      }
    }
  }, [deleteSaleMutation, invalidateStats]);

  const handleBulkDelete = useCallback(async (saleIds: string[]) => {
    try {
      await bulkDeleteSalesMutation.mutateAsync({ 
        saleIds, 
        reason: "Bulk deletion from sales page" 
      });
      refetch();
      invalidateStats();
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to delete sales:', error);
      }
    }
  }, [bulkDeleteSalesMutation, refetch, invalidateStats]);

  // Create columns with action handlers
  const columns = createSaleColumns(
    {
      onView: handleViewSale,
      onEdit: handleEditSale,
      onComplete: handleCompleteSale,
      onCancel: handleCancelSale,
      onRefund: handleRefundSale,
      onGenerateReceipt: handleGenerateReceipt,
      onGenerateReceiptWide: handleGenerateReceiptWide,
      onDelete: handleDeleteSale,
    },
    {
      isCompleting: completeSaleMutation.isPending,
      isCancelling: cancelSaleMutation.isPending,
      isRefunding: refundSaleMutation.isPending,
      isDeleting: deleteSaleMutation.isPending,
      isGeneratingReceipt: generateReceiptMutation.isPending,
      isGeneratingReceiptWide: generateReceiptWideMutation.isPending,
    }
  );

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">Gagal memuat data penjualan</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Coba Lagi
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      {/* Header Actions */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Manajemen Penjualan</h2>
          <p className="text-muted-foreground">
            Kelola semua transaksi penjualan di apotek Anda
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExport} disabled={exportSalesMutation.isPending}>
            <Download className="mr-2 h-4 w-4" />
            Ekspor
          </Button>

          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>

          <Button onClick={handleCreateSale}>
            <Plus className="mr-2 h-4 w-4" />
            Transaksi Baru
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full">
            <DataTable<Sale, any>
              columns={columns}
              data={data?.data || []}
              meta={data?.meta || {
                total: 0,
                page: 1,
                limit: 10,
                totalPages: 1,
                hasNextPage: false,
                hasPreviousPage: false
              }}
              query={query}
              onQueryChange={handleQueryChange}
              loading={isLoading}
              searchPlaceholder="Cari berdasarkan nomor transaksi, nama pelanggan..."
              onRowClick={handleViewSale}
              filters={filters}
              onFilterChange={handleFilterChange}
              onBatchFilterChange={handleBatchFilterChange}
              onClearFilters={clearFilters}
              onBulkComplete={handleBulkComplete}
              onBulkCancel={handleBulkCancel}
              onBulkDelete={handleBulkDelete}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick View Modal */}
      <SalesQuickView
        sale={selectedSale}
        open={showQuickView}
        onOpenChange={setShowQuickView}
        onEdit={handleEditSale}
        onComplete={handleCompleteSale}
        onCancel={handleCancelSale}
        onRefund={handleRefundSale}
        onGenerateReceipt={handleGenerateReceipt}
        onGenerateReceiptWide={handleGenerateReceiptWide}
        onViewDetails={handleViewDetails}
      />

      {/* Receipt Modal */}
      <ReceiptModal
        open={showReceiptModal}
        onOpenChange={setShowReceiptModal}
        receiptData={selectedReceiptData}
        saleId={selectedSaleId || undefined}
        title={selectedReceiptData?.formatting?.paperWidth === 80 ? 'Struk Lebar' : 'Struk Transaksi'}
        isLoading={generateReceiptMutation.isPending || generateReceiptWideMutation.isPending}
      />
    </div>
  );
}
