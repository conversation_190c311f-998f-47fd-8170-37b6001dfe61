'use client';

import { useMemo } from 'react';
import { useImmer } from 'use-immer';
import { useRouter } from 'next/navigation';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Save } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { CustomerSelector } from '@/components/ui/customer-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { DiscountTypeSelector } from '@/components/ui/discount-type-selector';
import { SaleItemsTable } from '@/components/sales/SaleItemsTable';
import { Sale, UpdateSaleDto, PaymentMethod } from '@/types/sales';
import { Customer } from '@/types/customer';
import { useUpdateDraft, useCompleteSale } from '@/hooks/useSales';
import { formatCurrency } from '@/lib/utils';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { Input } from '../ui/input';

// Form validation schema
const saleItemSchema = z.object({
  id: z.string().optional(),
  productId: z.string().min(1, 'Produk harus dipilih'),
  unitId: z.string().min(1, 'Satuan harus dipilih'),
  quantity: z.number().min(1, 'Jumlah minimal 1'),
  unitPrice: z.number().min(0, 'Harga tidak boleh negatif'),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  discountValue: z.number().transform((val) => val || 0).optional(),
  notes: z.string().optional(),
});

const updateSaleSchema = z.object({
  saleNumber: z.string().optional(),
  customerId: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  amountPaid: z.number().min(0, 'Jumlah bayar tidak boleh negatif'),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  discountValue: z.number().min(0).optional(),
  taxAmount: z.number().min(0).optional(),
  notes: z.string().optional(),
  items: z.array(saleItemSchema).min(1, 'Minimal harus ada 1 item'),
});

type UpdateSaleFormData = z.infer<typeof updateSaleSchema>;

const PAYMENT_METHOD_OPTIONS = [
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer Bank' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

interface EditSaleFormClientProps {
  sale: Sale;
}

export function EditSaleFormClient({ sale }: EditSaleFormClientProps) {
  const router = useRouter();
  const [formState, updateFormState] = useImmer({
    isSubmitting: false,
    showConfirmDialog: false,
    submitAction: 'update' as 'update' | 'complete',
  });

  // Mutations
  const updateDraftMutation = useUpdateDraft();
  const completeSaleMutation = useCompleteSale();

  // Form setup
  const form = useForm<UpdateSaleFormData>({
    resolver: zodResolver(updateSaleSchema),
    defaultValues: {
      saleNumber: sale.saleNumber,
      customerId: sale.customerId || 'walk-in',
      customerName: sale.customerName || '',
      customerPhone: sale.customerPhone || '',
      paymentMethod: sale.paymentMethod || PaymentMethod.CASH,
      amountPaid: Number(sale.amountPaid) || 0,
      discountType: (sale.discountType as 'PERCENTAGE' | 'FIXED_AMOUNT') || 'FIXED_AMOUNT',
      discountValue: Number(sale.discountValue) || 0,
      taxAmount: Number(sale.taxAmount) || 0,
      notes: sale.notes || '',
      items: sale.saleItems.map(item => ({
        id: item.id,
        productId: item.productId,
        unitId: item.unitId,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice),
        discountType: (item.discountType as 'PERCENTAGE' | 'FIXED_AMOUNT') || 'PERCENTAGE',
        discountValue: Number(item.discountValue) || 0,
        notes: item.notes || '',
      })),
    },
  });

  const { fields: itemFields, append: appendItem, remove: removeItem } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Watch form values for calculations
  const watchedItems = form.watch('items');
  const watchedDiscountType = form.watch('discountType');
  const watchedDiscountValue = form.watch('discountValue');
  const watchedTaxAmount = form.watch('taxAmount');
  const watchedAmountPaid = form.watch('amountPaid');

  // Memoize individual calculation steps to prevent unnecessary recalculations
  const itemsSubtotal = useMemo(() => {
    return watchedItems.reduce((sum, item) => {
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
      const itemDiscount = item.discountType === 'PERCENTAGE'
        ? itemTotal * ((item.discountValue || 0) / 100)
        : (item.discountValue || 0);
      return sum + itemTotal - itemDiscount;
    }, 0);
  }, [watchedItems]);

  // Calculate sale discount separately
  const saleDiscount = useMemo(() => {
    return watchedDiscountType === 'PERCENTAGE'
      ? itemsSubtotal * ((watchedDiscountValue || 0) / 100)
      : (watchedDiscountValue || 0);
  }, [itemsSubtotal, watchedDiscountType, watchedDiscountValue]);

  // Calculate final totals
  const totals = useMemo(() => {
    const subtotalAfterDiscount = itemsSubtotal - saleDiscount;
    const taxAmount = watchedTaxAmount || 0;
    const totalAmount = subtotalAfterDiscount + taxAmount;
    const changeAmount = Math.max(0, (watchedAmountPaid || 0) - totalAmount);

    return {
      itemsSubtotal,
      saleDiscount,
      subtotalAfterDiscount,
      taxAmount,
      totalAmount,
      changeAmount,
    };
  }, [itemsSubtotal, saleDiscount, watchedTaxAmount, watchedAmountPaid]);

  // Add new item
  const handleAddItem = () => {
    appendItem({
      productId: '',
      unitId: '',
      quantity: 1,
      unitPrice: 0,
      discountType: 'PERCENTAGE',
      discountValue: 0,
      notes: '',
    });
  };

  // Remove item
  const handleRemoveItem = (index: number) => {
    removeItem(index);
  };

  // Handle customer creation
  const handleCustomerCreated = (customer: Customer) => {
    toast.success(`Pelanggan ${customer.fullName} berhasil dibuat dan dipilih!`);
  };

  // Handle form submission
  const handleSubmit = async (action: 'update' | 'complete') => {
    updateFormState(draft => {
      draft.submitAction = action;
      draft.showConfirmDialog = true;
    });
  };

  // Function to handle direct completion without going through update flow
  const handleCompleteSale = async () => {
    // Prevent multiple clicks
    if (formState.isSubmitting) return;
    
    // Check if payment is sufficient
    const formData = form.getValues();
    const epsilon = 0.01; // Small value to account for floating point precision
    
    if (totals.totalAmount - formData.amountPaid > epsilon) {
      toast.error(`Jumlah pembayaran (${formatCurrency(formData.amountPaid)}) tidak mencukupi untuk total transaksi (${formatCurrency(totals.totalAmount)})`);
      return;
    }
    
    updateFormState(draft => {
      draft.isSubmitting = true;
    });

    try {
      // First update the draft with current form data
      const saleData: UpdateSaleDto = {
        ...formData,
        customerId: formData.customerId === 'walk-in' ? undefined : formData.customerId,
        discountType: formData.discountType,
        items: formData.items.map(item => ({
          productId: item.productId,
          unitId: item.unitId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountType: item.discountType,
          discountValue: item.discountValue,
          notes: item.notes,
        })),
      };
      
      // Show updating message
      toast.success('Memperbarui data transaksi...');
      
      // Update the draft first
      await updateDraftMutation.mutateAsync({ id: sale.id, data: saleData });
      
      // Then complete the sale
      toast.success('Menyelesaikan transaksi...');
      
      // Continue with the API call to complete the sale
      completeSaleMutation.mutate(sale.id, {
        onSuccess: (result) => {
          router.push('/dashboard/sales');
        },
        onError: (error: any) => {
          const errorMessage = error?.response?.data?.message || 'Gagal menyelesaikan transaksi. Silakan cek status transaksi.';
          toast.error(errorMessage);
          console.error('Complete sale error:', error);
          
          // Reset submitting state if there's an error
          updateFormState(draft => {
            draft.isSubmitting = false;
          });
        }
      });
    } catch (error) {
      toast.error('Gagal menyelesaikan transaksi. Silakan coba lagi.');
      
      // Reset submitting state only if we haven't redirected
      updateFormState(draft => {
        draft.isSubmitting = false;
      });
    }
  };

  const confirmSubmit = async () => {
    updateFormState(draft => {
      draft.isSubmitting = true;
      draft.showConfirmDialog = false;
    });

    try {
      const formData = form.getValues();

      // Prepare the sale data
      const saleData: UpdateSaleDto = {
        ...formData,
        // Handle walk-in customer: convert 'walk-in' to undefined for backend
        customerId: formData.customerId === 'walk-in' ? undefined : formData.customerId,
        discountType: formData.discountType,
        items: formData.items.map(item => ({
          productId: item.productId,
          unitId: item.unitId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountType: item.discountType,
          discountValue: item.discountValue,
          notes: item.notes,
        })),
      };

      if (formState.submitAction === 'update') {
        await updateDraftMutation.mutateAsync({ id: sale.id, data: saleData });
        toast.success('Draft penjualan berhasil diperbarui');
      } else {
        // First update the draft
        await updateDraftMutation.mutateAsync({ id: sale.id, data: saleData });
        
        // Then complete the sale
        await completeSaleMutation.mutateAsync(sale.id);
        toast.success('Transaksi berhasil diselesaikan');
        router.push('/dashboard/sales');
      }
    } catch (error) {
      console.error('Failed to update or complete sale:', error);
      toast.error('Gagal menyelesaikan transaksi. Silakan coba lagi.');
    } finally {
      updateFormState(draft => {
        draft.isSubmitting = false;
      });
    }
  };

  return (
    <div className="md:py-6 py-4 px-4 lg:px-6 flex-1 flex flex-col space-y-3 overflow-y-auto">
    <h1 className="text-2xl font-bold mb-6">Edit Draft Penjualan #{sale.saleNumber}</h1>
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="mb-2"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Kembali
        </Button>
        <div className="flex gap-2">
          <AlertDialogWrapper
            variant="primary"
            title="Simpan Perubahan"
            description="Apakah Anda yakin ingin menyimpan perubahan pada draft ini?"
            confirmText="Simpan"
            cancelText="Batal"
            pendingText="Menyimpan..."
            handler={() => confirmSubmit()}
          >
            <Button
              variant="outline"
              disabled={formState.isSubmitting}
              className="ml-auto"
            >
              <Save className="h-4 w-4 mr-2" />
              Simpan Draft
            </Button>
          </AlertDialogWrapper>
          
          <AlertDialogWrapper
            variant="primary"
            title="Selesaikan Transaksi"
            description="Apakah Anda yakin ingin menyelesaikan transaksi ini? Stok akan dialokasikan dan transaksi tidak dapat diubah lagi."
            confirmText="Selesaikan"
            cancelText="Batal"
            pendingText="Menyelesaikan..."
            handler={handleCompleteSale}
          >
            <Button
              disabled={formState.isSubmitting}
              className="ml-auto"
            >
              Selesaikan Transaksi
            </Button>
          </AlertDialogWrapper>
        </div>
      </div>

      <Form {...form}>
        <form className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <CustomerInfoCard form={form} onCustomerCreated={handleCustomerCreated} />
            <PaymentSummaryCard form={form} totals={totals} />
          </div>

          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-4 border-b">
              <h3 className="text-lg font-medium">Item Penjualan</h3>
              <p className="text-sm text-muted-foreground">
                Tambahkan produk yang akan dijual
              </p>
            </div>
            <SaleItemsTable
              form={form}
              itemFields={itemFields}
              onAddItem={handleAddItem}
              onRemoveItem={handleRemoveItem}
            />
          </div>

          <PaymentDetailsCard
            form={form}
            totals={totals}
            watchedAmountPaid={watchedAmountPaid}
          />
        </form>
      </Form>
    </div>
  );
}

// Customer Info Card Component
function CustomerInfoCard({
  form,
  onCustomerCreated
}: {
  form: UseFormReturn<any>;
  onCustomerCreated: (customer: Customer) => void;
}) {
  return (
    <div className="bg-white rounded-lg border shadow-sm">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Informasi Pelanggan</h3>
        <p className="text-sm text-muted-foreground">
          Pilih pelanggan atau masukkan data pelanggan baru
        </p>
      </div>
      <div className="p-4 space-y-4">
        <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Pelanggan</FormLabel>
              <FormControl>
                <CustomerSelector
                  value={field.value}
                  onValueChange={(customerId) => {
                    const previousValue = field.value;
                    field.onChange(customerId);
                    
                    // Only clear fields when changing customer types, not on initial load
                    if (previousValue !== customerId) {
                      if (customerId && customerId !== 'walk-in') {
                        // We would need to fetch customer details here
                        // For now, just clear the fields if switching from walk-in to registered
                        form.setValue('customerName', '');
                        form.setValue('customerPhone', '');
                      } else if (customerId === 'walk-in' && previousValue !== '') {
                        // Only clear fields when switching TO walk-in from a registered customer
                        form.setValue('customerName', '');
                        form.setValue('customerPhone', '');
                      }
                    }
                  }}
                  onCustomerCreated={onCustomerCreated}
                  enableCustomerCreation={true}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {form.watch('customerId') === 'walk-in' && (
            <div className="grid md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="customerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nama</FormLabel>
                  <FormControl>
                    <Input                      
                      {...field}
                      placeholder="Nama Pelanggan"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
  
            <FormField
              control={form.control}
              name="customerPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>No. Telepon</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="No. Telepon"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Payment Summary Card Component
function PaymentSummaryCard({
  form,
  totals
}: {
  form: UseFormReturn<any>;
  totals: any;
}) {
  return (
    <div className="bg-white rounded-lg border shadow-sm">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Ringkasan Pembayaran</h3>
        <p className="text-sm text-muted-foreground">
          Informasi total pembayaran
        </p>
      </div>
      <div className="p-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Subtotal</span>
            <span>{formatCurrency(totals.itemsSubtotal)}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Diskon</span>
            <span>- {formatCurrency(totals.saleDiscount)}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Pajak</span>
            <span>{formatCurrency(totals.taxAmount)}</span>
          </div>
          
          <Separator />
          
          <div className="flex justify-between font-medium">
            <span>Total</span>
            <span>{formatCurrency(totals.totalAmount)}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Dibayar</span>
            <span>{formatCurrency(form.watch('amountPaid') || 0)}</span>
          </div>
          
          <div className="flex justify-between font-medium">
            <span>Kembalian</span>
            <span>{formatCurrency(totals.changeAmount)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Payment Details Card Component
function PaymentDetailsCard({
  form,
  totals,
  watchedAmountPaid
}: {
  form: UseFormReturn<any>;
  totals: any;
  watchedAmountPaid: number;
}) {
  return (
    <div className="bg-white rounded-lg border shadow-sm">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Detail Pembayaran</h3>
        <p className="text-sm text-muted-foreground">
          Masukkan informasi pembayaran
        </p>
      </div>
      <div className="p-4 space-y-4">
        <div className="grid md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Metode Pembayaran</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih metode pembayaran" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {PAYMENT_METHOD_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="amountPaid"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Jumlah Dibayar</FormLabel>
                <FormControl>
                  <LiveCurrencyInput
                    value={field.value}
                    onValueChange={(value) => {
                      field.onChange(value);
                    }}
                    placeholder="0"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <FormField
              control={form.control}
              name="discountType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipe Diskon</FormLabel>
                  <FormControl>
                    <DiscountTypeSelector
                      value={field.value}
                      onValueChange={(value) => field.onChange(value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <div>
            <FormField
              control={form.control}
              name="discountValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Nilai Diskon {form.watch('discountType') === 'PERCENTAGE' ? '(%)' : ''}
                  </FormLabel>
                  <FormControl>
                    {form.watch('discountType') === 'PERCENTAGE' ? (
                      <Input
                        type="number"
                        value={field.value === 0 ? '' : field.value}
                        min={0}
                        max={100}
                        placeholder="0"
                        onChange={(e) => {
                          const value = e.target.value === '' ? 0 : Math.min(100, Number(e.target.value));
                          field.onChange(value);
                        }}
                      />
                    ) : (
                      <LiveCurrencyInput
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                        }}
                        placeholder="0"
                      />
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <FormField
          control={form.control}
          name="taxAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Pajak</FormLabel>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                  }}
                  placeholder="0"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Catatan</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Catatan tambahan untuk transaksi ini"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}