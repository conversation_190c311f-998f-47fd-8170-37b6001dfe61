'use client';

import { useState, useEffect } from 'react';
import { Session } from 'next-auth';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Trash2,
  Plus,
  Minus,
  CreditCard,
  Receipt,
  Percent,
  AlertCircle,
  Banknote,
  FileText,
  Edit3
} from 'lucide-react';
import { Product, UnitType } from '@/types/product';
import { formatCurrency } from '@/lib/utils';
import { useCreateSale } from '@/hooks/useSales';
import { useProductStock } from '@/hooks/useInventory';
import { toast } from 'sonner';
import { CreateSaleDto, CreateSaleItemDto, PaymentMethod } from '@/types/sales';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { UnitSelectorWithStock } from '@/components/ui/unit-selector-with-stock';
import {
  calculateAvailableStockWithCartImpact,
  convertPosCartItemsToCalculatorFormat,
  extractUnitHierarchies
} from '@/lib/utils/cart-stock-calculator';
import { usePosStockRefresh } from '@/hooks/useInventory';

interface CartItem {
  productId: string;
  productName: string;
  productCode: string;
  quantity: number;
  price: number;
  unitId: string;
  unitName: string;
  discount: number; // Percentage discount
  subtotal: number;
  availableUnits: {
    id: string;
    name: string;
    price: number;
    stock: number;
    conversionFactor: number;
  }[];
}

interface PosCartPanelProps {
  user: Session['user'];
  selectedProduct: Product | undefined;
  onProductSelected: () => void;
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
}

export function PosCartPanel({
  user,
  selectedProduct,
  onProductSelected,
  customerId = 'walk-in',
  customerName = '',
  customerPhone = ''
}: PosCartPanelProps) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [total, setTotal] = useState(0);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(PaymentMethod.CASH);
  const [customerCash, setCustomerCash] = useState<string>('');
  const [change, setChange] = useState(0);
  const [currentProductId, setCurrentProductId] = useState<string | null>(null);

  const { mutateAsync: createSale, isPending: isLoading } = useCreateSale();
  const stockRefresh = usePosStockRefresh();
  const { data: stockData } = useProductStock(currentProductId || '');

  // Set current product ID when a product is selected
  useEffect(() => {
    if (selectedProduct) {
      setCurrentProductId(selectedProduct.id);
    }
  }, [selectedProduct]);

  // Add selected product to cart when stock data is available
  useEffect(() => {
    if (selectedProduct && stockData && currentProductId === selectedProduct.id) {
      // Find the base unit for pricing
      const baseUnit = selectedProduct.baseUnit;

      // Get all available units with prices from unit hierarchies
      const availableUnits = selectedProduct.unitHierarchies?.map(uh => {
        const conversionFactor = Number(uh.conversionFactor) || 1;

        // Get stock information for this unit
        const unitStock = stockData[uh.unit.id] || {
          quantityOnHand: 0,
          quantityAllocated: 0,
          availableStock: 0
        };

        return {
          id: uh.unit.id,
          name: uh.unit.name,
          price: uh.sellingPrice || 0,
          stock: unitStock.availableStock,
          conversionFactor: conversionFactor
        };
      }) || [];

      // Make sure base unit is included
      if (!availableUnits.some(u => u.id === baseUnit.id)) {
        const baseUnitStock = stockData[baseUnit.id] || {
          quantityOnHand: 0,
          quantityAllocated: 0,
          availableStock: 0
        };

        availableUnits.push({
          id: baseUnit.id,
          name: baseUnit.name,
          price: selectedProduct.unitHierarchies?.find(uh => uh.unitId === baseUnit.id)?.sellingPrice || 0,
          stock: baseUnitStock.availableStock,
          conversionFactor: 1 // Base unit has conversion factor of 1
        });
      }

      // Find the price from unit hierarchies if available
      const unitHierarchy = selectedProduct.unitHierarchies?.find(
        (uh) => uh.unitId === baseUnit.id
      );

      const price = unitHierarchy?.sellingPrice || 0;

      const existingItemIndex = cartItems.findIndex(
        (item) => item.productId === selectedProduct.id && item.unitId === baseUnit.id
      );

      if (existingItemIndex >= 0) {
        // Update quantity if product already in cart - check stock first
        const existingItem = cartItems[existingItemIndex];
        const newQuantity = existingItem.quantity + 1;

        // Create a temporary item for stock calculation
        const tempItem: CartItem = {
          productId: selectedProduct.id,
          productName: selectedProduct.name,
          productCode: selectedProduct.code,
          quantity: existingItem.quantity,
          price: price,
          unitId: baseUnit.id,
          unitName: baseUnit.name,
          discount: 0,
          subtotal: price,
          availableUnits
        };

        // Get cart-aware stock for this unit (excluding the existing item)
        const cartAwareStock = getCartAwareStock(tempItem, baseUnit.id, existingItemIndex);

        // Check if we have enough stock for the new quantity
        if (newQuantity > cartAwareStock) {
          toast.error(`Stok tidak cukup. Tersedia: ${cartAwareStock} ${baseUnit.name} (setelah dikurangi keranjang)`);
          // Clear selected product and current product ID
          setCurrentProductId(null);
          onProductSelected();
          return;
        }

        const updatedItems = [...cartItems];
        updatedItems[existingItemIndex].quantity = newQuantity;
        updatedItems[existingItemIndex].subtotal =
          calculateSubtotal(
            updatedItems[existingItemIndex].price,
            newQuantity,
            updatedItems[existingItemIndex].discount
          );

        setCartItems(updatedItems);
        toast.success(`${selectedProduct.name} ditambahkan ke keranjang`);
      } else {
        // Add new product to cart - check stock first
        const newItem: CartItem = {
          productId: selectedProduct.id,
          productName: selectedProduct.name,
          productCode: selectedProduct.code,
          quantity: 1,
          price: price,
          unitId: baseUnit.id,
          unitName: baseUnit.name,
          discount: 0,
          subtotal: price,
          availableUnits
        };

        // Get cart-aware stock for this unit (no exclusion needed for new item)
        const cartAwareStock = getCartAwareStock(newItem, baseUnit.id);

        // Check if we have enough stock for 1 unit
        if (1 > cartAwareStock) {
          toast.error(`Stok tidak cukup. Tersedia: ${cartAwareStock} ${baseUnit.name} (setelah dikurangi keranjang)`);
          // Clear selected product and current product ID
          setCurrentProductId(null);
          onProductSelected();
          return;
        }

        setCartItems([...cartItems, newItem]);
        toast.success(`${selectedProduct.name} ditambahkan ke keranjang`);
      }

      // Clear selected product and current product ID
      setCurrentProductId(null);
      onProductSelected();
    }
  }, [selectedProduct, stockData, currentProductId, cartItems, onProductSelected]);

  // Calculate subtotal with discount
  const calculateSubtotal = (price: number, quantity: number, discount: number) => {
    const subtotal = price * quantity;
    const discountAmount = subtotal * (discount / 100);
    return subtotal - discountAmount;
  };

  // Calculate total whenever cart items change
  useEffect(() => {
    const newTotal = cartItems.reduce((sum, item) => sum + item.subtotal, 0);
    setTotal(newTotal);

    // Recalculate change
    const cashAmount = parseFloat(customerCash) || 0;
    setChange(Math.max(0, cashAmount - newTotal));
  }, [cartItems, customerCash]);

  const handleQuantityChange = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    const updatedItems = [...cartItems];
    const item = updatedItems[index];

    // Get cart-aware stock for this unit (excluding current item)
    const cartAwareStock = getCartAwareStock(item, item.unitId, index);

    // Check if we have enough stock
    if (newQuantity > cartAwareStock) {
      const selectedUnit = item.availableUnits.find(u => u.id === item.unitId);
      toast.error(`Stok tidak cukup. Tersedia: ${cartAwareStock} ${selectedUnit?.name || 'unit'} (setelah dikurangi keranjang)`);
      return;
    }

    updatedItems[index].quantity = newQuantity;
    updatedItems[index].subtotal = calculateSubtotal(
      updatedItems[index].price,
      newQuantity,
      updatedItems[index].discount
    );

    setCartItems(updatedItems);
  };

  const handleDiscountChange = (index: number, discount: number) => {
    if (discount < 0 || discount > 100) return;

    const updatedItems = [...cartItems];
    updatedItems[index].discount = discount;
    updatedItems[index].subtotal = calculateSubtotal(
      updatedItems[index].price,
      updatedItems[index].quantity,
      discount
    );

    setCartItems(updatedItems);
  };

  const handlePriceChange = (index: number, newPrice: number) => {
    if (newPrice < 0) return;

    const updatedItems = [...cartItems];
    updatedItems[index].price = newPrice;
    updatedItems[index].subtotal = calculateSubtotal(
      newPrice,
      updatedItems[index].quantity,
      updatedItems[index].discount
    );

    setCartItems(updatedItems);
  };

  const handleUnitChange = (index: number, unitId: string) => {
    const updatedItems = [...cartItems];
    const item = updatedItems[index];
    const selectedUnit = item.availableUnits.find(u => u.id === unitId);

    if (selectedUnit) {
      // Check if changing to this unit would create a duplicate product-unit combination
      const existingItemIndex = updatedItems.findIndex(
        (cartItem, cartIndex) =>
          cartIndex !== index &&
          cartItem.productId === item.productId &&
          cartItem.unitId === unitId
      );

      if (existingItemIndex >= 0) {
        // Merge with existing item
        const existingItem = updatedItems[existingItemIndex];

        // Convert current item quantity to target unit for merging calculation
        const currentItemUnit = item.availableUnits.find(u => u.id === item.unitId);
        const targetUnit = item.availableUnits.find(u => u.id === unitId);

        if (!currentItemUnit || !targetUnit) {
          toast.error('Unit tidak ditemukan');
          return;
        }

        // Convert current item quantity to target unit
        const currentItemQuantityInTargetUnit = Math.round(
          (item.quantity * currentItemUnit.conversionFactor) / targetUnit.conversionFactor
        );

        const newQuantity = existingItem.quantity + currentItemQuantityInTargetUnit;

        // Get cart-aware stock for the target unit (excluding BOTH items being merged)
        const availableForMerge = getCartAwareStock(item, unitId, undefined, [index, existingItemIndex]);

        // Check if we have enough stock for the merged quantity
        if (newQuantity > availableForMerge) {
          toast.error(`Stok tidak cukup untuk menggabungkan. Tersedia: ${availableForMerge} ${selectedUnit.name} (setelah dikurangi keranjang)`);
          return;
        }

        // Update existing item quantity and remove current item
        existingItem.quantity = newQuantity;
        existingItem.subtotal = calculateSubtotal(
          existingItem.price,
          newQuantity,
          existingItem.discount
        );

        // Remove the current item since we merged it
        updatedItems.splice(index, 1);

        toast.success(`Item digabungkan dengan ${item.productName} (${selectedUnit.name}) yang sudah ada. Total: ${newQuantity} ${selectedUnit.name}`);
      } else {
        // Get cart-aware stock for the new unit (excluding current item)
        const cartAwareStock = getCartAwareStock(item, unitId, index);

        // Check if we have enough stock for current quantity
        if (item.quantity > cartAwareStock) {
          toast.error(`Stok tidak cukup. Tersedia: ${cartAwareStock} ${selectedUnit.name} (setelah dikurangi keranjang)`);
          return;
        }

        // Update current item unit
        item.unitId = selectedUnit.id;
        item.unitName = selectedUnit.name;
        item.price = selectedUnit.price;
        item.subtotal = calculateSubtotal(
          selectedUnit.price,
          item.quantity,
          item.discount
        );
      }
    }

    setCartItems(updatedItems);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = cartItems.filter((_, i) => i !== index);
    setCartItems(updatedItems);
  };

  const handleCashChange = (value: string) => {
    // Only allow numbers and decimal point
    const regex = /^[0-9]*\.?[0-9]*$/;
    if (value === '' || regex.test(value)) {
      setCustomerCash(value);
    }
  };

  const handlePaymentMethodChange = (method: PaymentMethod) => {
    setSelectedPaymentMethod(method);
    // Reset cash-related fields when switching away from cash
    if (method !== PaymentMethod.CASH) {
      setCustomerCash('');
      setChange(0);
    }
  };

  const handleProcessSale = async () => {
    if (cartItems.length === 0) {
      toast.error('Keranjang kosong');
      return;
    }

    // Validation based on payment method
    if (selectedPaymentMethod === PaymentMethod.CASH) {
      const cashAmount = parseFloat(customerCash) || 0;
      if (cashAmount < total) {
        toast.error('Pembayaran kurang');
        return;
      }
    } else {
      // For non-cash payments, we assume the payment has been confirmed
      // In a real implementation, you might want additional confirmation steps
      if (!confirm(`Konfirmasi pembayaran ${getPaymentMethodLabel(selectedPaymentMethod)} sebesar ${formatCurrency(total)}?`)) {
        return;
      }
    }

    // Check stock availability for all items using cart-aware calculations
    // We need to validate the entire cart as a whole, not individual items
    const productGroups = new Map<string, CartItem[]>();

    // Group cart items by product
    cartItems.forEach(item => {
      if (!productGroups.has(item.productId)) {
        productGroups.set(item.productId, []);
      }
      productGroups.get(item.productId)!.push(item);
    });

    // Validate each product group
    for (const [, productItems] of productGroups) {
      const firstItem = productItems[0];
      const productData = createProductDataForCartItem(firstItem);
      const unitHierarchies = extractUnitHierarchies(productData);

      // Calculate total demand in base units for this product
      let totalDemandInBaseUnits = 0;
      for (const item of productItems) {
        const unitHierarchy = unitHierarchies.find(uh => uh.unitId === item.unitId);
        if (unitHierarchy) {
          totalDemandInBaseUnits += item.quantity * unitHierarchy.conversionFactor;
        }
      }

      // Get original base stock
      const baseUnitHierarchy = unitHierarchies.find(uh => uh.level === 0);
      if (baseUnitHierarchy) {
        const baseUnit = firstItem.availableUnits.find(u => u.id === baseUnitHierarchy.unitId);
        const availableBaseStock = baseUnit?.stock || 0;

        if (totalDemandInBaseUnits > availableBaseStock) {
          // Find which unit to show in error message (prefer the first item's unit)
          const errorUnit = firstItem.availableUnits.find(u => u.id === firstItem.unitId);
          const availableInErrorUnit = Math.floor(availableBaseStock / (unitHierarchies.find(uh => uh.unitId === firstItem.unitId)?.conversionFactor || 1));

          toast.error(`Stok ${firstItem.productName} tidak cukup. Tersedia: ${availableInErrorUnit} ${errorUnit?.name || 'unit'}`);
          return;
        }
      }
    }

    try {
      const saleItems: CreateSaleItemDto[] = cartItems.map(item => ({
        productId: item.productId,
        unitId: item.unitId,
        quantity: item.quantity,
        unitPrice: item.price,
        discountType: item.discount > 0 ? 'PERCENTAGE' : undefined,
        discountValue: item.discount > 0 ? item.discount : undefined
      }));

      // Calculate amount paid based on payment method
      const amountPaid = selectedPaymentMethod === PaymentMethod.CASH
        ? parseFloat(customerCash) || 0
        : total; // For non-cash payments, amount paid equals total

      const saleData: CreateSaleDto = {
        items: saleItems,
        paymentMethod: selectedPaymentMethod,
        amountPaid: amountPaid,
        cashierId: user.id,
        customerId: customerId !== 'walk-in' ? customerId : undefined,
        // Include customer name and phone for walk-in customers
        customerName: customerId === 'walk-in' && customerName ? customerName : undefined,
        customerPhone: customerId === 'walk-in' && customerPhone ? customerPhone : undefined
      };

      await createSale(saleData);

      // CRITICAL: Refresh stock data for all products in the sale
      // This ensures the POS interface shows updated stock levels immediately
      const soldProductIds = cartItems.map(item => item.productId);
      stockRefresh.refreshAfterSale(soldProductIds);

      toast.success('Transaksi berhasil');
      setCartItems([]);
      setCustomerCash('');
      setChange(0);
    } catch (error) {
      toast.error('Gagal memproses transaksi');
      console.error(error);
    }
  };

  // Quick payment buttons
  const getQuickPaymentAmounts = () => {
    const amounts = [];

    // Exact amount
    amounts.push({
      label: 'Pas',
      value: total
    });

    // Round to nearest 10k, 50k, 100k
    [10000, 50000, 100000].forEach(roundTo => {
      if (total < roundTo) {
        amounts.push({
          label: formatCurrency(roundTo),
          value: roundTo
        });
      } else {
        const rounded = Math.ceil(total / roundTo) * roundTo;
        amounts.push({
          label: formatCurrency(rounded),
          value: rounded
        });
      }
    });

    return amounts;
  };



  // Helper function to get payment method label in Indonesian
  const getPaymentMethodLabel = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return 'Tunai';
      case PaymentMethod.TRANSFER:
        return 'Transfer';
      case PaymentMethod.CREDIT:
        return 'Kredit';
      case PaymentMethod.GIRO:
        return 'Giro';
      default:
        return method;
    }
  };

  // Helper function to convert cart item units to UnitSelectorWithStock format
  const convertToUnitSelectorFormat = (item: CartItem, excludeItemIndex?: number) => {
    return item.availableUnits.map(unit => {
      // Get cart-aware stock for this unit
      const cartAwareStock = getCartAwareStock(item, unit.id, excludeItemIndex);

      return {
        id: unit.id,
        name: unit.name,
        abbreviation: unit.name.toLowerCase().substring(0, 3), // Simple abbreviation
        type: UnitType.PACKAGE,
        isBaseUnit: unit.conversionFactor === 1,
        description: `Harga: ${formatCurrency(unit.price)}`,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        stockInfo: {
          quantityOnHand: unit.stock, // Original stock
          quantityAllocated: 0,
          availableStock: cartAwareStock // Cart-aware stock
        }
      };
    });
  };

  // Helper function to create product data for cart-aware stock calculation
  const createProductDataForCartItem = (item: CartItem) => {
    // Sort units by conversion factor to assign proper levels
    const sortedUnits = [...item.availableUnits].sort((a, b) => a.conversionFactor - b.conversionFactor);

    return {
      id: item.productId,
      unitHierarchies: sortedUnits.map((unit, index) => ({
        unitId: unit.id,
        conversionFactor: unit.conversionFactor,
        level: index // Level 0 = base unit (lowest conversion factor)
      }))
    };
  };

  // Helper function to get cart-aware stock for a specific unit
  // excludeItemIndex: exclude this cart item from the calculation (for quantity validation)
  // excludeItemIndices: exclude multiple cart items from the calculation (for merging validation)
  const getCartAwareStock = (item: CartItem, unitId: string, excludeItemIndex?: number, excludeItemIndices?: number[]): number => {
    const productData = createProductDataForCartItem(item);
    const unitHierarchies = extractUnitHierarchies(productData);

    // Filter cart items to exclude specified items
    let filteredCartItems = cartItems;

    if (excludeItemIndices && excludeItemIndices.length > 0) {
      // Exclude multiple items (for merging scenarios)
      filteredCartItems = cartItems.filter((_, index) => !excludeItemIndices.includes(index));
    } else if (excludeItemIndex !== undefined) {
      // Exclude single item (for quantity validation)
      filteredCartItems = cartItems.filter((_, index) => index !== excludeItemIndex);
    }

    // Convert cart items to calculator format
    const cartStockItems = convertPosCartItemsToCalculatorFormat(filteredCartItems);

    // Create original stock data from item's available units
    const originalStockData: Record<string, any> = {};
    item.availableUnits.forEach(unit => {
      originalStockData[unit.id] = {
        quantityOnHand: unit.stock,
        quantityAllocated: 0,
        availableStock: unit.stock
      };
    });

    // Calculate updated stock considering cart impact
    const updatedStockData = calculateAvailableStockWithCartImpact(
      originalStockData,
      cartStockItems,
      item.productId,
      unitHierarchies
    );

    return updatedStockData[unitId]?.availableStock || 0;
  };

  return (
    <div className="h-full flex flex-col border rounded-md overflow-hidden">
      <div className="flex-none bg-muted/50 p-2 border-b">
        <div className="text-sm font-medium">Keranjang Belanja</div>
      </div>

      <div className="flex-1 min-h-0 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Left Panel - Cart Table */}
          <ResizablePanel defaultSize={65} minSize={40} className="min-w-0">
            <ScrollArea className="h-full">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow>
                    <TableHead className="w-[30%]">Produk</TableHead>
                    <TableHead>Satuan</TableHead>
                    <TableHead className="text-right">Harga</TableHead>
                    <TableHead className="text-center">Jumlah</TableHead>
                    <TableHead className="text-center">Diskon</TableHead>
                    <TableHead className="text-right">Subtotal</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cartItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center h-24 text-muted-foreground">
                        Keranjang kosong
                      </TableCell>
                    </TableRow>
                  ) : (
                    cartItems.map((item, index) => (
                      <TableRow key={`${item.productId}-${item.unitId}-${index}`}>
                        <TableCell>
                          <div className="font-medium text-sm">{item.productName}</div>
                          <div className="text-xs text-muted-foreground">
                            {item.productCode} • {item.unitName}
                          </div>
                        </TableCell>
                        <TableCell>
                          <UnitSelectorWithStock
                            units={convertToUnitSelectorFormat(item, index)}
                            value={item.unitId}
                            onValueChange={(value) => handleUnitChange(index, value)}
                            placeholder="Pilih satuan"
                            className="h-8 w-40"
                            showStockInfo={true}
                            enableCartAwareStock={false} // Disable since we're already using cart-aware stock in units
                            cartItems={cartItems}
                            productData={createProductDataForCartItem(item)}
                            compactMode={true}
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            <span>{formatCurrency(item.price)}</span>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  type="button"
                                  size="icon"
                                  variant="ghost"
                                  className="h-6 w-6 opacity-60 hover:opacity-100"
                                >
                                  <Edit3 className="h-3 w-3" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-56">
                                <div className="space-y-2">
                                  <div className="font-medium text-sm">Edit Harga</div>
                                  <div className="text-xs text-muted-foreground">
                                    Harga asli: {formatCurrency(item.availableUnits.find(u => u.id === item.unitId)?.price || 0)}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Input
                                      type="number"
                                      min="0"
                                      step="0.01"
                                      value={item.price}
                                      onChange={(e) => handlePriceChange(index, Number(e.target.value))}
                                      className="h-8"
                                      placeholder="Harga baru"
                                    />
                                  </div>
                                  <div className="grid grid-cols-3 gap-1 mt-2">
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      className="h-7 text-xs"
                                      onClick={() => {
                                        const originalPrice = item.availableUnits.find(u => u.id === item.unitId)?.price || 0;
                                        handlePriceChange(index, originalPrice);
                                      }}
                                    >
                                      Reset
                                    </Button>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      className="h-7 text-xs"
                                      onClick={() => {
                                        const originalPrice = item.availableUnits.find(u => u.id === item.unitId)?.price || 0;
                                        handlePriceChange(index, originalPrice * 0.9);
                                      }}
                                    >
                                      -10%
                                    </Button>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      className="h-7 text-xs"
                                      onClick={() => {
                                        const originalPrice = item.availableUnits.find(u => u.id === item.unitId)?.price || 0;
                                        handlePriceChange(index, originalPrice * 0.95);
                                      }}
                                    >
                                      -5%
                                    </Button>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center">
                            <Button
                              type="button"
                              size="icon"
                              variant="outline"
                              className="h-7 w-7"
                              onClick={() => handleQuantityChange(index, item.quantity - 1)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center mx-1">{item.quantity}</span>
                            <Button
                              type="button"
                              size="icon"
                              variant="outline"
                              className="h-7 w-7"
                              onClick={() => handleQuantityChange(index, item.quantity + 1)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" size="sm" className="h-7 w-16">
                                  {item.discount > 0 ? `${item.discount}%` : '-'}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-48">
                                <div className="space-y-2">
                                  <div className="font-medium text-sm">Diskon (%)</div>
                                  <div className="flex items-center">
                                    <Input
                                      type="number"
                                      min="0"
                                      max="100"
                                      value={item.discount}
                                      onChange={(e) => handleDiscountChange(index, Number(e.target.value))}
                                      className="h-8"
                                    />
                                    <Percent className="h-4 w-4 ml-2 text-muted-foreground" />
                                  </div>
                                  <div className="grid grid-cols-4 gap-1 mt-2">
                                    {[0, 5, 10, 15, 20, 25, 30, 50].map((discountValue) => (
                                      <Button
                                        key={discountValue}
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        className="h-7"
                                        onClick={() => handleDiscountChange(index, discountValue)}
                                      >
                                        {discountValue}%
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </div>
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(item.subtotal)}
                        </TableCell>
                        <TableCell>
                          <Button
                            type="button"
                            size="icon"
                            variant="ghost"
                            className="h-7 w-7"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Right Panel - Payment Controls */}
          <ResizablePanel defaultSize={35} minSize={25} maxSize={50} className="border-l bg-muted/30 flex flex-col">
            <ScrollArea className="h-full">
              <div className="p-4 space-y-2">
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-lg">
                    <span className="font-medium">Total:</span>
                    <span className="font-bold text-xl">{formatCurrency(total)}</span>
                  </div>

                  {/* Payment Method Selection */}
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="button"
                      variant={selectedPaymentMethod === PaymentMethod.CASH ? 'default' : 'outline'}
                      size="sm"
                      className="justify-start text-xs"
                      onClick={() => handlePaymentMethodChange(PaymentMethod.CASH)}
                    >
                      <Banknote className="mr-1 h-3 w-3" />
                      Tunai
                    </Button>
                    <Button
                      type="button"
                      variant={selectedPaymentMethod === PaymentMethod.TRANSFER ? 'default' : 'outline'}
                      size="sm"
                      className="justify-start text-xs"
                      onClick={() => handlePaymentMethodChange(PaymentMethod.TRANSFER)}
                    >
                      <CreditCard className="mr-1 h-3 w-3" />
                      Transfer
                    </Button>
                    <Button
                      type="button"
                      variant={selectedPaymentMethod === PaymentMethod.CREDIT ? 'default' : 'outline'}
                      size="sm"
                      className="justify-start text-xs"
                      onClick={() => handlePaymentMethodChange(PaymentMethod.CREDIT)}
                    >
                      <CreditCard className="mr-1 h-3 w-3" />
                      Kredit
                    </Button>
                    <Button
                      type="button"
                      variant={selectedPaymentMethod === PaymentMethod.GIRO ? 'default' : 'outline'}
                      size="sm"
                      className="justify-start text-xs"
                      onClick={() => handlePaymentMethodChange(PaymentMethod.GIRO)}
                    >
                      <FileText className="mr-1 h-3 w-3" />
                      Giro
                    </Button>
                  </div>

                  {/* Cash-specific fields - only show for cash payments */}
                  {selectedPaymentMethod === PaymentMethod.CASH && (
                    <>
                      <div className="flex flex-col space-y-1">
                        <label className="text-sm font-medium">Tunai:</label>
                        <Input
                          type="text"
                          value={customerCash}
                          onChange={(e) => handleCashChange(e.target.value)}
                          className="h-10 text-lg font-medium"
                          placeholder="0"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        {getQuickPaymentAmounts().map((amount, i) => (
                          <Button
                            key={i}
                            type="button"
                            variant="outline"
                            size="sm"
                            className="text-xs"
                            onClick={() => setCustomerCash(amount.value.toString())}
                          >
                            {amount.label}
                          </Button>
                        ))}
                      </div>

                      <div className="flex justify-between items-center my-2 text-lg">
                        <span className="font-medium">Kembalian:</span>
                        <span className={`font-bold ${change > 0 ? 'text-green-600' : ''}`}>
                          {formatCurrency(change)}
                        </span>
                      </div>
                    </>
                  )}

                  {/* Non-cash payment methods - show confirmation message */}
                  {selectedPaymentMethod !== PaymentMethod.CASH && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                      <div className="text-sm text-blue-800">
                        <strong>Pembayaran {getPaymentMethodLabel(selectedPaymentMethod)}</strong>
                        <p className="mt-1 text-blue-600">
                          Pastikan pembayaran sebesar {formatCurrency(total)} telah diterima melalui metode {getPaymentMethodLabel(selectedPaymentMethod).toLowerCase()}.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 mt-auto">
                  <Button
                    className="w-full"
                    size="lg"
                    disabled={
                      isLoading ||
                      cartItems.length === 0 ||
                      (selectedPaymentMethod === PaymentMethod.CASH && parseFloat(customerCash) < total)
                    }
                    onClick={handleProcessSale}
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    Proses Pembayaran
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full"
                    disabled={isLoading || cartItems.length === 0}
                  >
                    <Receipt className="mr-2 h-4 w-4" />
                    Cetak Struk
                  </Button>

                  {selectedPaymentMethod === PaymentMethod.CASH && parseFloat(customerCash) < total && parseFloat(customerCash) > 0 && (
                    <div className="flex items-center text-destructive text-sm">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Pembayaran kurang {formatCurrency(total - parseFloat(customerCash))}
                    </div>
                  )}
                </div>
              </div>
            </ScrollArea>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}