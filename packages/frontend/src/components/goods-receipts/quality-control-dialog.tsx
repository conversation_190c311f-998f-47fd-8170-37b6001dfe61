'use client';

import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Upload, X, Image, Camera, FileText, CheckCircle, XCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { GoodsReceipt, QualityControlStatus } from '@/types/goods-receipt';
import { useUpdateQualityControl, useUploadQualityPhoto, useQualityPhotos, useDeleteQualityPhoto } from '@/hooks/useGoodsReceipts';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface QualityControlDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  goodsReceipt: GoodsReceipt;
}

const qualityControlSchema = z.object({
  qualityStatus: z.nativeEnum(QualityControlStatus),
  qualityNotes: z.string().optional(),
  temperatureCheck: z.boolean().optional(),
  packagingCheck: z.boolean().optional(),
  documentationCheck: z.boolean().optional(),
  bpomCheck: z.boolean().optional(),
});

type QualityControlFormData = z.infer<typeof qualityControlSchema>;

export function QualityControlDialog({ open, onOpenChange, goodsReceipt }: QualityControlDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const updateQualityControlMutation = useUpdateQualityControl();
  const uploadPhotoMutation = useUploadQualityPhoto();
  const deletePhotoMutation = useDeleteQualityPhoto();
  const { data: photosData } = useQualityPhotos(goodsReceipt.id);

  const form = useForm<QualityControlFormData>({
    resolver: zodResolver(qualityControlSchema),
    defaultValues: {
      qualityStatus: goodsReceipt.qualityStatus,
      qualityNotes: goodsReceipt.qualityNotes || '',
      temperatureCheck: false,
      packagingCheck: false,
      documentationCheck: false,
      bpomCheck: false,
    },
  });

  const onSubmit = async (data: QualityControlFormData) => {
    setIsSubmitting(true);
    try {
      // Upload photos first if any
      for (const file of selectedFiles) {
        await uploadPhotoMutation.mutateAsync({
          id: goodsReceipt.id,
          file,
        });
      }

      // Update quality control
      await updateQualityControlMutation.mutateAsync({
        id: goodsReceipt.id,
        data: {
          ...data,
          inspectionDate: new Date().toISOString(),
        },
      });

      onOpenChange(false);
      form.reset();
      setSelectedFiles([]);
    } catch (error) {
      // Error is handled by the mutation
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    setSelectedFiles([]);
    onOpenChange(false);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length !== files.length) {
      toast.error('Hanya file gambar yang diperbolehkan');
    }

    setSelectedFiles(prev => [...prev, ...imageFiles]);
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleDeletePhoto = async (photoUrl: string) => {
    try {
      await deletePhotoMutation.mutateAsync({
        id: goodsReceipt.id,
        photoUrl,
      });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const getQualityStatusColor = (status: QualityControlStatus) => {
    switch (status) {
      case QualityControlStatus.PASSED:
        return 'text-green-600';
      case QualityControlStatus.FAILED:
        return 'text-red-600';
      case QualityControlStatus.CONDITIONAL:
        return 'text-yellow-600';
      case QualityControlStatus.EXEMPTED:
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Kontrol Kualitas
          </DialogTitle>
          <DialogDescription>
            Lakukan kontrol kualitas untuk penerimaan barang {goodsReceipt.receiptNumber}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh]">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Receipt Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Ringkasan Penerimaan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Supplier:</span>
                      <div className="font-medium">{goodsReceipt.supplier.name}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total Item:</span>
                      <div className="font-medium">{goodsReceipt.items.length}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total Nilai:</span>
                      <div className="font-medium">{formatCurrency(goodsReceipt.totalAmount)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Status Saat Ini:</span>
                      <Badge className={getQualityStatusColor(goodsReceipt.qualityStatus)}>
                        {goodsReceipt.qualityStatus}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Items Quality Check */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Pemeriksaan Item</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produk</TableHead>
                        <TableHead>Batch</TableHead>
                        <TableHead>Exp. Date</TableHead>
                        <TableHead>Qty</TableHead>
                        <TableHead>Kondisi</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {goodsReceipt.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">{item.product.name}</span>
                              <span className="text-xs text-muted-foreground">{item.product.code}</span>
                            </div>
                          </TableCell>
                          <TableCell>{item.batchNumber || '-'}</TableCell>
                          <TableCell>{item.expiryDate || '-'}</TableCell>
                          <TableCell>{item.quantityReceived} {item.unit.abbreviation}</TableCell>
                          <TableCell>
                            <Badge variant={item.conditionOnReceipt === 'good' ? 'default' : 'destructive'}>
                              {item.conditionOnReceipt || 'Tidak diketahui'}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {/* Quality Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Status Kualitas</CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="qualityStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status Kualitas Keseluruhan</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih status kualitas" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value={QualityControlStatus.PENDING}>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-gray-500" />
                                Menunggu
                              </div>
                            </SelectItem>
                            <SelectItem value={QualityControlStatus.PASSED}>
                              <div className="flex items-center gap-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                Lulus
                              </div>
                            </SelectItem>
                            <SelectItem value={QualityControlStatus.FAILED}>
                              <div className="flex items-center gap-2">
                                <XCircle className="w-4 h-4 text-red-600" />
                                Gagal
                              </div>
                            </SelectItem>
                            <SelectItem value={QualityControlStatus.CONDITIONAL}>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-yellow-500" />
                                Bersyarat
                              </div>
                            </SelectItem>
                            <SelectItem value={QualityControlStatus.EXEMPTED}>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-blue-500" />
                                Dikecualikan
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Quality Checks */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Checklist Pemeriksaan</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="temperatureCheck"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-medium">Pemeriksaan Suhu</FormLabel>
                          <FormDescription>
                            Produk disimpan dan dikirim pada suhu yang sesuai dengan persyaratan
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="packagingCheck"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-medium">Pemeriksaan Kemasan</FormLabel>
                          <FormDescription>
                            Kemasan produk dalam kondisi baik, tidak rusak, dan sesuai standar
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="documentationCheck"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-medium">Pemeriksaan Dokumentasi</FormLabel>
                          <FormDescription>
                            Dokumen lengkap dan sesuai dengan persyaratan regulasi
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bpomCheck"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-medium">Verifikasi BPOM</FormLabel>
                          <FormDescription>
                            Produk terdaftar di BPOM dan memiliki izin edar yang valid
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Photo Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    Dokumentasi Foto
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Existing Photos */}
                  {photosData?.photos && photosData.photos.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Foto yang sudah ada:</h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {photosData.photos.map((photoUrl, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={photoUrl}
                              alt={`Quality photo ${index + 1}`}
                              className="w-full h-24 object-cover rounded-md border"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => handleDeletePhoto(photoUrl)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* File Upload */}
                  <div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Foto Kualitas
                    </Button>
                  </div>

                  {/* Selected Files Preview */}
                  {selectedFiles.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Foto yang akan diupload:</h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {selectedFiles.map((file, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Preview ${index + 1}`}
                              className="w-full h-24 object-cover rounded-md border"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute top-1 right-1"
                              onClick={() => handleRemoveFile(index)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                            <div className="absolute bottom-1 left-1 right-1 bg-black/50 text-white text-xs p-1 rounded truncate">
                              {file.name}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quality Notes */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Catatan Kualitas
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="qualityNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Catatan Pemeriksaan</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tambahkan catatan detail tentang hasil pemeriksaan kualitas, kondisi produk, atau temuan khusus..."
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Catatan ini akan menjadi bagian dari dokumentasi kualitas dan dapat digunakan untuk referensi di masa depan
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <DialogFooter className="gap-2">
                <Button type="button" variant="outline" onClick={handleCancel} disabled={isSubmitting}>
                  Batal
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Menyimpan...' : 'Simpan Hasil Pemeriksaan'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
