'use client';

import {
  Package,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Users,
  Zap,
  BarChart3,
  Timer,
  Target,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { useGoodsReceiptStats } from '@/hooks/useGoodsReceipts';
import { formatCurrency } from '@/lib/utils';

interface GoodsReceiptStatsCardsProps {
  period?: string;
}

export function GoodsReceiptStatsCards({ period }: GoodsReceiptStatsCardsProps = {}) {
  const { data: stats, isLoading, error } = useGoodsReceiptStats(period);

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <Card className="col-span-full">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <AlertTriangle className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground mb-1">Gagal memuat statistik penerimaan barang</p>
            <p className="text-xs text-muted-foreground">
              {error instanceof Error ? error.message : 'Terjadi kesalahan yang tidak diketahui'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  // Ensure all required fields have safe defaults to prevent runtime errors

  const safeStats = {
    totalReceipts: stats.totalReceipts ?? 0,
    pendingInspection: stats.pendingInspection ?? 0,
    approved: stats.approved ?? 0,
    rejected: stats.rejected ?? 0,
    qualityPassRate: stats.qualityPassRate ?? 0,
    averageInspectionTime: stats.averageInspectionTime ?? 0,
    totalValue: stats.totalValue ?? 0,
    qualityTrends: stats.qualityTrends ?? [],
    supplierQualityStats: stats.supplierQualityStats ?? [],
    processingTimeAnalytics: {
      averageProcessingTime: stats.processingTimeAnalytics?.averageProcessingTime ?? 0,
      averageInspectionTime: stats.processingTimeAnalytics?.averageInspectionTime ?? 0,
      totalReceipts: stats.processingTimeAnalytics?.totalReceipts ?? 0,
      efficiencyMetrics: {
        fastProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.fastProcessing ?? 0,
        slowProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.slowProcessing ?? 0,
        averageProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.averageProcessing ?? 0,
        fastPercentage: stats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0,
        slowPercentage: stats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0,
      },
    },
    volumeTrends: stats.volumeTrends ?? [],
  };

  const qualityPassRatePercentage = safeStats.qualityPassRate * 100;

  // Helper functions for analytics with additional safety checks
  const getQualityTrend = () => {
    if (!safeStats.qualityTrends || safeStats.qualityTrends.length < 2) return null;
    const recent = safeStats.qualityTrends.slice(-2);
    const current = recent[1]?.count || 0;
    const previous = recent[0]?.count || 0;
    return current > previous ? 'up' : current < previous ? 'down' : 'stable';
  };

  const getVolumeTrend = () => {
    if (!safeStats.volumeTrends || safeStats.volumeTrends.length < 2) return null;
    const recent = safeStats.volumeTrends.slice(-2);
    const current = recent[1]?.count || 0;
    const previous = recent[0]?.count || 0;
    return current > previous ? 'up' : current < previous ? 'down' : 'stable';
  };

  const getTopSupplier = () => {
    if (!safeStats.supplierQualityStats || safeStats.supplierQualityStats.length === 0) return null;
    return safeStats.supplierQualityStats[0]; // Already sorted by quality rate
  };

  const qualityTrend = getQualityTrend();
  const volumeTrend = getVolumeTrend();
  const topSupplier = getTopSupplier();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Receipts with Volume Trend */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Penerimaan</CardTitle>
          <div className="flex items-center gap-1">
            {volumeTrend === 'up' && <TrendingUp className="h-3 w-3 text-green-600" />}
            {volumeTrend === 'down' && <TrendingDown className="h-3 w-3 text-red-600" />}
            <Package className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeStats.totalReceipts.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            {volumeTrend === 'up' && '↗ Meningkat dari periode sebelumnya'}
            {volumeTrend === 'down' && '↘ Menurun dari periode sebelumnya'}
            {(!volumeTrend || volumeTrend === 'stable') && 'Semua penerimaan barang'}
          </p>
        </CardContent>
      </Card>

      {/* Total Value */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Nilai</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(safeStats.totalValue)}</div>
          <p className="text-xs text-muted-foreground">
            Nilai total penerimaan barang
          </p>
        </CardContent>
      </Card>

      {/* Pending Inspection with Alert */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Menunggu Inspeksi</CardTitle>
          <div className="flex items-center gap-1">
            {safeStats.pendingInspection > 10 && <AlertTriangle className="h-3 w-3 text-orange-600" />}
            <Clock className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {safeStats.pendingInspection.toLocaleString()}
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Perlu kontrol kualitas
            </p>
            {safeStats.pendingInspection > 10 && (
              <Badge variant="outline" className="text-xs">
                Prioritas Tinggi
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Approved with Quality Trend */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Disetujui</CardTitle>
          <div className="flex items-center gap-1">
            {qualityTrend === 'up' && <TrendingUp className="h-3 w-3 text-green-600" />}
            {qualityTrend === 'down' && <TrendingDown className="h-3 w-3 text-red-600" />}
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {safeStats.approved.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            {qualityTrend === 'up' && '↗ Kualitas membaik'}
            {qualityTrend === 'down' && '↘ Perlu perhatian'}
            {(!qualityTrend || qualityTrend === 'stable') && 'Lulus kontrol kualitas'}
          </p>
        </CardContent>
      </Card>

      {/* Quality Pass Rate with Performance Indicator */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tingkat Kelulusan</CardTitle>
          <div className="flex items-center gap-1">
            {qualityPassRatePercentage >= 95 && <Target className="h-3 w-3 text-green-600" />}
            {qualityPassRatePercentage < 80 && <AlertTriangle className="h-3 w-3 text-red-600" />}
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${qualityPassRatePercentage >= 95 ? 'text-green-600' :
            qualityPassRatePercentage >= 80 ? 'text-blue-600' : 'text-red-600'
            }`}>
            {qualityPassRatePercentage.toFixed(1)}%
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              {qualityPassRatePercentage >= 95 ? 'Sangat baik' :
                qualityPassRatePercentage >= 80 ? 'Rata-rata kualitas baik' : 'Perlu perbaikan'}
            </p>
            {qualityPassRatePercentage >= 95 && (
              <Badge variant="outline" className="text-xs text-green-600">
                Excellent
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Processing Efficiency */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Efisiensi Proses</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0).toFixed(0)}%
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Proses cepat (&lt;24 jam)
            </p>
            {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0) >= 70 && (
              <Badge variant="outline" className="text-xs text-green-600">
                Efisien
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Average Processing Time */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Rata-rata Proses</CardTitle>
          <Timer className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {Math.round(safeStats.processingTimeAnalytics?.averageProcessingTime ?? 0)}h
          </div>
          <p className="text-xs text-muted-foreground">
            Waktu rata-rata pemrosesan
          </p>
        </CardContent>
      </Card>

      {/* Rejected (if any) */}
      {safeStats.rejected > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ditolak</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {safeStats.rejected.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Tidak memenuhi standar
            </p>
          </CardContent>
        </Card>
      )}

      {/* Top Supplier Quality (if available) */}
      {topSupplier && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Supplier Terbaik</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-green-600 mb-1">
              {((topSupplier.qualityRate ?? 0) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground truncate">
              {topSupplier.supplierName || 'Unknown Supplier'}
            </p>
            <p className="text-xs text-muted-foreground">
              {topSupplier.totalReceipts || 0} penerimaan
            </p>
          </CardContent>
        </Card>
      )}

      {/* Volume Analytics */}
      {safeStats.volumeTrends && safeStats.volumeTrends.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tren Volume</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {safeStats.volumeTrends.slice(-1)[0]?.count || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Penerimaan hari terakhir
            </p>
          </CardContent>
        </Card>
      )}

      {/* Slow Processing Alert (if significant) */}
      {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0) > 20 && (
        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Proses Lambat</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0).toFixed(0)}%
            </div>
            <div className="flex items-center gap-2">
              <p className="text-xs text-muted-foreground">
                Proses &gt;72 jam
              </p>
              <Badge variant="outline" className="text-xs text-orange-600">
                Perhatian
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Average Inspection Time (if available) */}
      {safeStats.averageInspectionTime > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Inspeksi</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(safeStats.averageInspectionTime)}h
            </div>
            <p className="text-xs text-muted-foreground">
              Waktu proses inspeksi
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
