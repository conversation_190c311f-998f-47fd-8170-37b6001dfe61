'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft, Package, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { GoodsReceiptForm } from '@/components/goods-receipts/goods-receipt-form';
import { useGoodsReceipt, useUpdateGoodsReceipt } from '@/hooks/useGoodsReceipts';
import { CreateGoodsReceiptDto, GoodsReceiptFormData } from '@/types/goods-receipt';

interface EditGoodsReceiptPageClientProps {
  goodsReceiptId: string;
}

export function EditGoodsReceiptPageClient({ goodsReceiptId }: EditGoodsReceiptPageClientProps) {
  const router = useRouter();
  const { data: goodsReceipt, isLoading, error, refetch } = useGoodsReceipt(goodsReceiptId);
  const updateGoodsReceiptMutation = useUpdateGoodsReceipt();

  const handleSubmit = async (data: CreateGoodsReceiptDto) => {
    try {
      await updateGoodsReceiptMutation.mutateAsync({
        id: goodsReceiptId,
        data,
      });
      router.push(`/dashboard/goods-receipts/${goodsReceiptId}`);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleRefresh = () => {
    refetch();
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Penerimaan Barang</h1>
            <p className="text-muted-foreground">Gagal memuat data penerimaan barang</p>
          </div>
        </div>

        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">Gagal memuat data penerimaan barang</p>
              <Button onClick={handleRefresh} variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Penerimaan Barang</h1>
            <p className="text-muted-foreground">Memuat data...</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Form Penerimaan Barang</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!goodsReceipt) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Penerimaan Barang</h1>
            <p className="text-muted-foreground">Penerimaan barang tidak ditemukan</p>
          </div>
        </div>
      </div>
    );
  }

  // Convert goods receipt data to form data
  const initialData: Partial<GoodsReceiptFormData> = {
    purchaseOrderId: goodsReceipt.purchaseOrderId || '',
    supplierId: goodsReceipt.supplierId,
    receiptDate: goodsReceipt.receiptDate.split('T')[0], // Convert to date string
    deliveryDate: goodsReceipt.deliveryDate?.split('T')[0] || '',
    invoiceNumber: goodsReceipt.invoiceNumber || '',
    deliveryNote: goodsReceipt.deliveryNote || '',
    deliveredBy: goodsReceipt.deliveredBy || '',
    receivedBy: goodsReceipt.receivedBy || '',
    deliveryCondition: goodsReceipt.deliveryCondition || '',
    notes: goodsReceipt.notes || '',
    internalNotes: goodsReceipt.internalNotes || '',
    items: goodsReceipt.items.map(item => ({
      productId: item.productId,
      unitId: item.unitId,
      quantityOrdered: item.quantityOrdered,
      quantityReceived: item.quantityReceived,
      unitPrice: item.unitPrice,
      batchNumber: item.batchNumber || '',
      expiryDate: item.expiryDate?.split('T')[0] || '',
      manufacturingDate: item.manufacturingDate?.split('T')[0] || '',
      storageLocation: item.storageLocation || '',
      conditionOnReceipt: item.conditionOnReceipt || '',
      notes: item.notes || '',
      product: item.product,
      unit: item.unit,
    })),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Package className="h-6 w-6" />
            Edit Penerimaan Barang
          </h1>
          <p className="text-muted-foreground">
            Edit penerimaan barang {goodsReceipt.receiptNumber}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Form Penerimaan Barang</CardTitle>
        </CardHeader>
        <CardContent>
          <GoodsReceiptForm
            initialData={initialData}
            onSubmit={handleSubmit}
            isSubmitting={updateGoodsReceiptMutation.isPending}
            mode="edit"
          />
        </CardContent>
      </Card>
    </div>
  );
}
