'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Package,
  RefreshCw,
  Edit,
  CheckCircle,
  XCircle,
  FileText,
  Camera,
  Download,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { QualityControlDialog } from '@/components/goods-receipts/quality-control-dialog';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import {
  useGoodsReceipt,
  useApproveGoodsReceipt,
  useRejectGoodsReceipt,
  useCompleteGoodsReceipt,
  useDeleteGoodsReceipt
} from '@/hooks/useGoodsReceipts';
import { GoodsReceiptStatus, QualityControlStatus } from '@/types/goods-receipt';
import { formatCurrency, formatDate, formatDateTime } from '@/lib/utils';

interface GoodsReceiptDetailPageClientProps {
  goodsReceiptId: string;
}

function getStatusBadge(status: GoodsReceiptStatus) {
  const statusConfig = {
    [GoodsReceiptStatus.PENDING]: { label: 'Menunggu', variant: 'secondary' as const },
    [GoodsReceiptStatus.IN_INSPECTION]: { label: 'Inspeksi', variant: 'default' as const },
    [GoodsReceiptStatus.APPROVED]: { label: 'Disetujui', variant: 'default' as const },
    [GoodsReceiptStatus.PARTIALLY_APPROVED]: { label: 'Sebagian Disetujui', variant: 'outline' as const },
    [GoodsReceiptStatus.REJECTED]: { label: 'Ditolak', variant: 'destructive' as const },
    [GoodsReceiptStatus.COMPLETED]: { label: 'Selesai', variant: 'default' as const },
  };

  const config = statusConfig[status];
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

function getQualityStatusBadge(status: QualityControlStatus) {
  const statusConfig = {
    [QualityControlStatus.PENDING]: { label: 'Menunggu', variant: 'secondary' as const },
    [QualityControlStatus.PASSED]: { label: 'Lulus', variant: 'default' as const },
    [QualityControlStatus.FAILED]: { label: 'Gagal', variant: 'destructive' as const },
    [QualityControlStatus.CONDITIONAL]: { label: 'Bersyarat', variant: 'outline' as const },
    [QualityControlStatus.EXEMPTED]: { label: 'Dikecualikan', variant: 'outline' as const },
  };

  const config = statusConfig[status];
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

export function GoodsReceiptDetailPageClient({ goodsReceiptId }: GoodsReceiptDetailPageClientProps) {
  const router = useRouter();
  const [showQualityControl, setShowQualityControl] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);

  const { data: goodsReceipt, isLoading, error, refetch } = useGoodsReceipt(goodsReceiptId);
  const approveGoodsReceiptMutation = useApproveGoodsReceipt();
  const rejectGoodsReceiptMutation = useRejectGoodsReceipt();
  const completeGoodsReceiptMutation = useCompleteGoodsReceipt();
  const deleteGoodsReceiptMutation = useDeleteGoodsReceipt();

  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    router.push(`/dashboard/goods-receipts/${goodsReceiptId}/edit`);
  };

  const handleApprove = async () => {
    try {
      await approveGoodsReceiptMutation.mutateAsync(goodsReceiptId);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleReject = async (reason?: string) => {
    if (!reason) return;

    try {
      await rejectGoodsReceiptMutation.mutateAsync({
        id: goodsReceiptId,
        data: { reason },
      });
      setShowRejectDialog(false);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleComplete = async () => {
    try {
      await completeGoodsReceiptMutation.mutateAsync(goodsReceiptId);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleDelete = async () => {
    try {
      await deleteGoodsReceiptMutation.mutateAsync(goodsReceiptId);
      setShowDeleteDialog(false);
      router.push('/dashboard/goods-receipts');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Detail Penerimaan Barang</h1>
            <p className="text-muted-foreground">Gagal memuat data penerimaan barang</p>
          </div>
        </div>

        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">Gagal memuat data penerimaan barang</p>
              <Button onClick={() => refetch()} variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Detail Penerimaan Barang</h1>
            <p className="text-muted-foreground">Memuat data...</p>
          </div>
        </div>

        <Card>
          <CardContent className="space-y-4 py-8">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!goodsReceipt) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Detail Penerimaan Barang</h1>
            <p className="text-muted-foreground">Penerimaan barang tidak ditemukan</p>
          </div>
        </div>
      </div>
    );
  }

  const canEdit = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canApprove = goodsReceipt.status === GoodsReceiptStatus.IN_INSPECTION &&
    goodsReceipt.qualityStatus === QualityControlStatus.PASSED;
  const canReject = goodsReceipt.status === GoodsReceiptStatus.IN_INSPECTION;
  const canComplete = goodsReceipt.status === GoodsReceiptStatus.APPROVED;
  const canQualityControl = goodsReceipt.status === GoodsReceiptStatus.PENDING ||
    goodsReceipt.status === GoodsReceiptStatus.IN_INSPECTION;
  const canDelete = goodsReceipt.status === GoodsReceiptStatus.PENDING;

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={handleBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Kembali
            </Button>
            <div>
              <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
                <Package className="h-6 w-6" />
                {goodsReceipt.receiptNumber}
              </h1>
              <p className="text-muted-foreground">
                Detail penerimaan barang dari {goodsReceipt.supplier.name}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button onClick={() => refetch()} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>

            {canEdit && (
              <Button onClick={handleEdit} variant="outline" size="sm">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            )}

            {canQualityControl && (
              <Button onClick={() => setShowQualityControl(true)} variant="outline" size="sm">
                <Camera className="mr-2 h-4 w-4" />
                Kontrol Kualitas
              </Button>
            )}

            {canApprove && (
              <Button onClick={handleApprove} variant="default" size="sm">
                <CheckCircle className="mr-2 h-4 w-4" />
                Setujui
              </Button>
            )}

            {canReject && (
              <Button onClick={() => setShowRejectDialog(true)} variant="destructive" size="sm">
                <XCircle className="mr-2 h-4 w-4" />
                Tolak
              </Button>
            )}

            {canComplete && (
              <Button onClick={handleComplete} variant="default" size="sm">
                <CheckCircle className="mr-2 h-4 w-4" />
                Selesaikan
              </Button>
            )}

            {canDelete && (
              <Button onClick={() => setShowDeleteDialog(true)} variant="destructive" size="sm">
                <Trash2 className="mr-2 h-4 w-4" />
                Hapus
              </Button>
            )}
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <div className="mt-1">
                    {getStatusBadge(goodsReceipt.status)}
                  </div>
                </div>
                <Package className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status Kualitas</p>
                  <div className="mt-1">
                    {getQualityStatusBadge(goodsReceipt.qualityStatus)}
                  </div>
                </div>
                <CheckCircle className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Nilai</p>
                  <p className="text-2xl font-bold">{formatCurrency(goodsReceipt.totalAmount)}</p>
                </div>
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Informasi Penerimaan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Nomor Penerimaan</p>
                    <p className="font-medium">{goodsReceipt.receiptNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Tanggal Penerimaan</p>
                    <p>{formatDate(goodsReceipt.receiptDate)}</p>
                  </div>
                  {goodsReceipt.deliveryDate && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Tanggal Pengiriman</p>
                      <p>{formatDate(goodsReceipt.deliveryDate)}</p>
                    </div>
                  )}
                  {goodsReceipt.invoiceNumber && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Nomor Invoice</p>
                      <p>{goodsReceipt.invoiceNumber}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Items */}
            <Card>
              <CardHeader>
                <CardTitle>Item Penerimaan</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produk</TableHead>
                        <TableHead>Batch</TableHead>
                        <TableHead>Exp. Date</TableHead>
                        <TableHead className="text-right">Qty</TableHead>
                        <TableHead className="text-right">Harga</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {goodsReceipt.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">{item.product.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {item.product.code} • {item.unit.name}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>{item.batchNumber || '-'}</TableCell>
                          <TableCell>
                            {item.expiryDate ? formatDate(item.expiryDate) : '-'}
                          </TableCell>
                          <TableCell className="text-right">
                            {item.quantityReceived} {item.unit.abbreviation}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.unitPrice)}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.totalPrice)}
                          </TableCell>
                          <TableCell>
                            {getQualityStatusBadge(item.qualityStatus)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle>Supplier</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <p className="font-medium">{goodsReceipt.supplier.name}</p>
                    <p className="text-sm text-muted-foreground">{goodsReceipt.supplier.code}</p>
                  </div>
                  {goodsReceipt.supplier.city && (
                    <p className="text-sm text-muted-foreground">{goodsReceipt.supplier.city}</p>
                  )}
                  {goodsReceipt.supplier.phone && (
                    <p className="text-sm text-muted-foreground">{goodsReceipt.supplier.phone}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Purchase Order */}
            {goodsReceipt.purchaseOrder && (
              <Card>
                <CardHeader>
                  <CardTitle>Purchase Order</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <p className="font-medium">{goodsReceipt.purchaseOrder.orderNumber}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(goodsReceipt.purchaseOrder.orderDate)}
                      </p>
                    </div>
                    <p className="text-sm">
                      Total PO: {formatCurrency(goodsReceipt.purchaseOrder.totalAmount)}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Audit Trail */}
            <Card>
              <CardHeader>
                <CardTitle>Audit Trail</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium">Dibuat</p>
                    <p className="text-muted-foreground">{formatDateTime(goodsReceipt.createdAt)}</p>
                    {goodsReceipt.createdByUser && (
                      <p className="text-muted-foreground">oleh {goodsReceipt.createdByUser.name}</p>
                    )}
                  </div>
                  <Separator />
                  <div>
                    <p className="font-medium">Terakhir Diperbarui</p>
                    <p className="text-muted-foreground">{formatDateTime(goodsReceipt.updatedAt)}</p>
                    {goodsReceipt.updatedByUser && (
                      <p className="text-muted-foreground">oleh {goodsReceipt.updatedByUser.name}</p>
                    )}
                  </div>
                  {goodsReceipt.inspectionDate && (
                    <>
                      <Separator />
                      <div>
                        <p className="font-medium">Inspeksi</p>
                        <p className="text-muted-foreground">{formatDateTime(goodsReceipt.inspectionDate)}</p>
                        {goodsReceipt.inspectionByUser && (
                          <p className="text-muted-foreground">oleh {goodsReceipt.inspectionByUser.name}</p>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Notes */}
        {(goodsReceipt.notes || goodsReceipt.qualityNotes || goodsReceipt.internalNotes) && (
          <Card>
            <CardHeader>
              <CardTitle>Catatan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {goodsReceipt.notes && (
                <div>
                  <p className="font-medium text-sm">Catatan Umum</p>
                  <p className="text-sm text-muted-foreground mt-1">{goodsReceipt.notes}</p>
                </div>
              )}
              {goodsReceipt.qualityNotes && (
                <div>
                  <p className="font-medium text-sm">Catatan Kualitas</p>
                  <p className="text-sm text-muted-foreground mt-1">{goodsReceipt.qualityNotes}</p>
                </div>
              )}
              {goodsReceipt.internalNotes && (
                <div>
                  <p className="font-medium text-sm">Catatan Internal</p>
                  <p className="text-sm text-muted-foreground mt-1">{goodsReceipt.internalNotes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Quality Control Dialog */}
      {goodsReceipt && (
        <QualityControlDialog
          open={showQualityControl}
          onOpenChange={setShowQualityControl}
          goodsReceipt={goodsReceipt}
        />
      )}

      {/* Delete Confirmation */}
      <AlertDialogWrapper
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Hapus Penerimaan Barang"
        description={`Apakah Anda yakin ingin menghapus penerimaan barang ${goodsReceipt.receiptNumber}? Tindakan ini tidak dapat dibatalkan.`}
        confirmText="Hapus"
        cancelText="Batal"
        onConfirm={handleDelete}
        variant="destructive"
      />

      {/* Reject Confirmation */}
      <AlertDialogWrapper
        open={showRejectDialog}
        onOpenChange={setShowRejectDialog}
        title="Tolak Penerimaan Barang"
        description={`Berikan alasan penolakan untuk penerimaan barang ${goodsReceipt.receiptNumber}:`}
        confirmText="Tolak"
        cancelText="Batal"
        onConfirm={handleReject}
        variant="destructive"
        requiresInput
        inputPlaceholder="Masukkan alasan penolakan..."
        multilineInput
      />
    </>
  );
}
