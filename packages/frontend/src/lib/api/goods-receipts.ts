import { apiClient } from '../axios';
import {
  GoodsReceipt,
  GoodsReceiptQueryParams,
  GoodsReceiptListResponse,
  GoodsReceiptStatsResponse,
  CreateGoodsReceiptDto,
  UpdateGoodsReceiptDto,
  QualityControlUpdateDto,
  GoodsReceiptStatusUpdateDto,
  RejectGoodsReceiptDto,
} from '@/types/goods-receipt';

export const goodsReceiptsApi = {
  // Get all goods receipts with filtering and pagination
  getGoodsReceipts: async (params: GoodsReceiptQueryParams = {}): Promise<GoodsReceiptListResponse> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/goods-receipts?${searchParams.toString()}`);
    return response.data;
  },

  // Get a specific goods receipt by ID
  getGoodsReceipt: async (id: string): Promise<GoodsReceipt> => {
    const response = await apiClient.get(`/goods-receipts/${id}`);
    return response.data;
  },

  // Create a new goods receipt
  createGoodsReceipt: async (data: CreateGoodsReceiptDto): Promise<GoodsReceipt> => {
    const response = await apiClient.post('/goods-receipts', data);
    return response.data;
  },

  // Update an existing goods receipt
  updateGoodsReceipt: async (id: string, data: UpdateGoodsReceiptDto): Promise<GoodsReceipt> => {
    const response = await apiClient.patch(`/goods-receipts/${id}`, data);
    return response.data;
  },

  // Delete a goods receipt
  deleteGoodsReceipt: async (id: string): Promise<void> => {
    await apiClient.delete(`/goods-receipts/${id}`);
  },

  // Get goods receipt statistics
  getGoodsReceiptStats: async (period?: string): Promise<GoodsReceiptStatsResponse> => {
    try {
      const params = period ? { period } : {};
      const response = await apiClient.get('/goods-receipts/stats', { params });

      // Validate response data and provide defaults for missing fields
      const data = response.data;
      return {
        totalReceipts: data.totalReceipts || 0,
        pendingInspection: data.pendingInspection || 0,
        approved: data.approved || 0,
        rejected: data.rejected || 0,
        qualityPassRate: data.qualityPassRate || 0,
        averageInspectionTime: data.averageInspectionTime || 0,
        totalValue: data.totalValue || 0,
        statusStats: data.statusStats || {},
        qualityStats: data.qualityStats || {},
        recentReceipts: data.recentReceipts || [],
        qualityTrends: data.qualityTrends || [],
        supplierQualityStats: data.supplierQualityStats || [],
        processingTimeAnalytics: data.processingTimeAnalytics || {
          averageProcessingTime: 0,
          averageInspectionTime: 0,
          totalReceipts: 0,
          efficiencyMetrics: {
            fastProcessing: 0,
            slowProcessing: 0,
            averageProcessing: 0,
            fastPercentage: 0,
            slowPercentage: 0,
          },
        },
        volumeTrends: data.volumeTrends || [],
        period: data.period || 'all',
      };
    } catch (error) {
      console.error('Error fetching goods receipt statistics:', error);

      // Return fallback data structure to prevent UI crashes
      return {
        totalReceipts: 0,
        pendingInspection: 0,
        approved: 0,
        rejected: 0,
        qualityPassRate: 0,
        averageInspectionTime: 0,
        totalValue: 0,
        statusStats: {},
        qualityStats: {},
        recentReceipts: [],
        qualityTrends: [],
        supplierQualityStats: [],
        processingTimeAnalytics: {
          averageProcessingTime: 0,
          averageInspectionTime: 0,
          totalReceipts: 0,
          efficiencyMetrics: {
            fastProcessing: 0,
            slowProcessing: 0,
            averageProcessing: 0,
            fastPercentage: 0,
            slowPercentage: 0,
          },
        },
        volumeTrends: [],
        period: 'all',
      };
    }
  },

  // Quality control operations
  updateQualityControl: async (id: string, data: QualityControlUpdateDto): Promise<GoodsReceipt> => {
    const response = await apiClient.post(`/goods-receipts/${id}/quality-control`, data);
    return response.data;
  },

  // Status management operations
  updateStatus: async (id: string, data: GoodsReceiptStatusUpdateDto): Promise<GoodsReceipt> => {
    const response = await apiClient.patch(`/goods-receipts/${id}/status`, data);
    return response.data;
  },

  // Approve goods receipt
  approveGoodsReceipt: async (id: string): Promise<GoodsReceipt> => {
    const response = await apiClient.post(`/goods-receipts/${id}/approve`);
    return response.data;
  },

  // Reject goods receipt
  rejectGoodsReceipt: async (id: string, data: RejectGoodsReceiptDto): Promise<GoodsReceipt> => {
    const response = await apiClient.post(`/goods-receipts/${id}/reject`, data);
    return response.data;
  },

  // Complete goods receipt (finalize and create inventory)
  completeGoodsReceipt: async (id: string): Promise<GoodsReceipt> => {
    const response = await apiClient.post(`/goods-receipts/${id}/complete`);
    return response.data;
  },

  // Generate goods receipt number
  generateReceiptNumber: async (): Promise<{ receiptNumber: string }> => {
    const response = await apiClient.get('/goods-receipts/generate-number');
    return response.data;
  },

  // Validate goods receipt number
  validateReceiptNumber: async (number: string): Promise<{ isUnique: boolean }> => {
    const response = await apiClient.get(`/goods-receipts/validate-number/${encodeURIComponent(number)}`);
    return response.data;
  },

  // Export goods receipts
  exportGoodsReceipts: async (params: GoodsReceiptQueryParams = {}): Promise<Blob> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/goods-receipts/export?${searchParams.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Bulk operations
  bulkUpdateStatus: async (ids: string[], status: string): Promise<{ updated: number }> => {
    const response = await apiClient.patch('/goods-receipts/bulk-status', {
      ids,
      status,
    });
    return response.data;
  },

  bulkDelete: async (ids: string[]): Promise<{ deleted: number }> => {
    const response = await apiClient.delete('/goods-receipts/bulk', {
      data: { ids },
    });
    return response.data;
  },

  // Photo upload for quality documentation
  uploadQualityPhoto: async (id: string, file: File): Promise<{ url: string }> => {
    const formData = new FormData();
    formData.append('photo', file);

    const response = await apiClient.post(`/goods-receipts/${id}/quality-photo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get quality photos
  getQualityPhotos: async (id: string): Promise<{ photos: string[] }> => {
    const response = await apiClient.get(`/goods-receipts/${id}/quality-photos`);
    return response.data;
  },

  // Delete quality photo
  deleteQualityPhoto: async (id: string, photoUrl: string): Promise<void> => {
    await apiClient.delete(`/goods-receipts/${id}/quality-photo`, {
      data: { photoUrl },
    });
  },

  // Get goods receipts by purchase order
  getGoodsReceiptsByPurchaseOrder: async (purchaseOrderId: string): Promise<GoodsReceipt[]> => {
    const response = await apiClient.get(`/goods-receipts/by-purchase-order/${purchaseOrderId}`);
    return response.data;
  },

  // Get pending goods receipts for quality control
  getPendingQualityControl: async (): Promise<GoodsReceipt[]> => {
    const response = await apiClient.get('/goods-receipts/pending-quality-control');
    return response.data;
  },

  // Get goods receipts ready for completion
  getReadyForCompletion: async (): Promise<GoodsReceipt[]> => {
    const response = await apiClient.get('/goods-receipts/ready-for-completion');
    return response.data;
  },

  // Validate goods receipt for completion
  validateForCompletion: async (id: string): Promise<{ canComplete: boolean; reason?: string }> => {
    const response = await apiClient.get(`/goods-receipts/${id}/validate-completion`);
    return response.data;
  },

  // Get discrepancy report
  getDiscrepancyReport: async (id: string): Promise<{
    hasDiscrepancies: boolean;
    discrepancies: Array<{
      itemId: string;
      productName: string;
      ordered: number;
      received: number;
      difference: number;
      reason?: string;
    }>;
  }> => {
    const response = await apiClient.get(`/goods-receipts/${id}/discrepancy-report`);
    return response.data;
  },

  // Create goods receipt from purchase order
  createFromPurchaseOrder: async (purchaseOrderId: string): Promise<GoodsReceipt> => {
    const response = await apiClient.post(`/goods-receipts/from-purchase-order/${purchaseOrderId}`);
    return response.data;
  },
};
