import {
  calculateCartQuantityInBaseUnits,
  calculateAvailableStockWithCartImpact,
  convertPosCartItemsToCalculatorFormat,
  extractUnitHierarchies,
  type CartItem,
  type UnitHierarchy,
  type StockInfo
} from '../cart-stock-calculator';

describe('Cart Stock Calculator', () => {
  // Mock data for testing
  const mockUnitHierarchies: UnitHierarchy[] = [
    { unitId: 'tablet-id', conversionFactor: 1, level: 0 }, // Base unit
    { unitId: 'strip-id', conversionFactor: 10, level: 1 }, // 1 strip = 10 tablets
    { unitId: 'box-id', conversionFactor: 100, level: 2 }, // 1 box = 100 tablets
  ];

  const mockStockData: Record<string, StockInfo> = {
    'tablet-id': { quantityOnHand: 1000, quantityAllocated: 0, availableStock: 1000 },
    'strip-id': { quantityOnHand: 100, quantityAllocated: 0, availableStock: 100 },
    'box-id': { quantityOnHand: 10, quantityAllocated: 0, availableStock: 10 },
  };

  describe('calculateCartQuantityInBaseUnits', () => {
    it('should calculate correct base units for single item', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-1', unitId: 'box-id', quantity: 2, conversionFactor: 100 }
      ];

      const result = calculateCartQuantityInBaseUnits(cartItems, 'product-1', mockUnitHierarchies);
      expect(result).toBe(200); // 2 boxes × 100 tablets/box = 200 tablets
    });

    it('should calculate correct base units for multiple items of same product', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-1', unitId: 'box-id', quantity: 1, conversionFactor: 100 },
        { productId: 'product-1', unitId: 'strip-id', quantity: 5, conversionFactor: 10 },
        { productId: 'product-1', unitId: 'tablet-id', quantity: 25, conversionFactor: 1 }
      ];

      const result = calculateCartQuantityInBaseUnits(cartItems, 'product-1', mockUnitHierarchies);
      expect(result).toBe(175); // 100 + 50 + 25 = 175 tablets
    });

    it('should return 0 for product not in cart', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-1', unitId: 'box-id', quantity: 2, conversionFactor: 100 }
      ];

      const result = calculateCartQuantityInBaseUnits(cartItems, 'product-2', mockUnitHierarchies);
      expect(result).toBe(0);
    });

    it('should handle empty cart', () => {
      const cartItems: CartItem[] = [];
      const result = calculateCartQuantityInBaseUnits(cartItems, 'product-1', mockUnitHierarchies);
      expect(result).toBe(0);
    });
  });

  describe('calculateAvailableStockWithCartImpact', () => {
    it('should reduce available stock based on cart contents', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-1', unitId: 'box-id', quantity: 2, conversionFactor: 100 }
      ];

      const result = calculateAvailableStockWithCartImpact(
        mockStockData,
        cartItems,
        'product-1',
        mockUnitHierarchies
      );

      // Original: 1000 tablets, Cart: 200 tablets, Remaining: 800 tablets
      expect(result['tablet-id'].availableStock).toBe(800); // 800 tablets
      expect(result['strip-id'].availableStock).toBe(80);   // 80 strips (800/10)
      expect(result['box-id'].availableStock).toBe(8);      // 8 boxes (800/100)
    });

    it('should handle stock depletion correctly', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-1', unitId: 'box-id', quantity: 10, conversionFactor: 100 }
      ];

      const result = calculateAvailableStockWithCartImpact(
        mockStockData,
        cartItems,
        'product-1',
        mockUnitHierarchies
      );

      // All stock consumed
      expect(result['tablet-id'].availableStock).toBe(0);
      expect(result['strip-id'].availableStock).toBe(0);
      expect(result['box-id'].availableStock).toBe(0);
    });

    it('should not go below zero stock', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-1', unitId: 'box-id', quantity: 15, conversionFactor: 100 }
      ];

      const result = calculateAvailableStockWithCartImpact(
        mockStockData,
        cartItems,
        'product-1',
        mockUnitHierarchies
      );

      // Stock cannot go negative
      expect(result['tablet-id'].availableStock).toBe(0);
      expect(result['strip-id'].availableStock).toBe(0);
      expect(result['box-id'].availableStock).toBe(0);
    });

    it('should return original data when no cart items for product', () => {
      const cartItems: CartItem[] = [
        { productId: 'product-2', unitId: 'box-id', quantity: 2, conversionFactor: 100 }
      ];

      const result = calculateAvailableStockWithCartImpact(
        mockStockData,
        cartItems,
        'product-1',
        mockUnitHierarchies
      );

      expect(result).toEqual(mockStockData);
    });
  });

  describe('convertPosCartItemsToCalculatorFormat', () => {
    it('should convert POS cart items correctly', () => {
      const posCartItems = [
        {
          productId: 'product-1',
          unitId: 'box-id',
          quantity: 2,
          availableUnits: [
            { id: 'box-id', conversionFactor: 100 },
            { id: 'strip-id', conversionFactor: 10 }
          ]
        }
      ];

      const result = convertPosCartItemsToCalculatorFormat(posCartItems);

      expect(result).toEqual([
        {
          productId: 'product-1',
          unitId: 'box-id',
          quantity: 2,
          conversionFactor: 100
        }
      ]);
    });

    it('should handle missing conversion factor', () => {
      const posCartItems = [
        {
          productId: 'product-1',
          unitId: 'tablet-id',
          quantity: 5,
          availableUnits: [
            { id: 'tablet-id', conversionFactor: undefined } // Missing conversionFactor
          ]
        }
      ];

      const result = convertPosCartItemsToCalculatorFormat(posCartItems);

      expect(result[0].conversionFactor).toBe(1); // Default to 1
    });
  });

  describe('extractUnitHierarchies', () => {
    it('should extract unit hierarchies correctly', () => {
      const product = {
        unitHierarchies: [
          { unitId: 'tablet-id', conversionFactor: '1', level: 0 },
          { unitId: 'strip-id', conversionFactor: '10', level: 1 },
          { unitId: 'box-id', conversionFactor: '100', level: 2 }
        ]
      };

      const result = extractUnitHierarchies(product);

      expect(result).toEqual([
        { unitId: 'tablet-id', conversionFactor: 1, level: 0 },
        { unitId: 'strip-id', conversionFactor: 10, level: 1 },
        { unitId: 'box-id', conversionFactor: 100, level: 2 }
      ]);
    });

    it('should handle missing unit hierarchies', () => {
      const product = {};
      const result = extractUnitHierarchies(product);
      expect(result).toEqual([]);
    });

    it('should convert string conversion factors to numbers', () => {
      const product = {
        unitHierarchies: [
          { unitId: 'strip-id', conversionFactor: '10.5', level: 1 }
        ]
      };

      const result = extractUnitHierarchies(product);
      expect(result[0].conversionFactor).toBe(10.5);
    });
  });

  describe('Integration Test - Real World Scenario', () => {
    it('should handle complex pharmacy scenario correctly', () => {
      // Scenario: Medicine with 1000 tablets in stock
      // User adds 3 boxes + 2 strips to cart
      // Should show remaining availability for each unit level

      const cartItems: CartItem[] = [
        { productId: 'medicine-1', unitId: 'box-id', quantity: 3, conversionFactor: 100 },
        { productId: 'medicine-1', unitId: 'strip-id', quantity: 2, conversionFactor: 10 }
      ];

      const totalCartInBaseUnits = calculateCartQuantityInBaseUnits(
        cartItems,
        'medicine-1',
        mockUnitHierarchies
      );

      expect(totalCartInBaseUnits).toBe(320); // 300 + 20 = 320 tablets

      const updatedStock = calculateAvailableStockWithCartImpact(
        mockStockData,
        cartItems,
        'medicine-1',
        mockUnitHierarchies
      );

      // Remaining: 1000 - 320 = 680 tablets
      expect(updatedStock['tablet-id'].availableStock).toBe(680);
      expect(updatedStock['strip-id'].availableStock).toBe(68);  // 680/10
      expect(updatedStock['box-id'].availableStock).toBe(6);    // 680/100
    });
  });
});
