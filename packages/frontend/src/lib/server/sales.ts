import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createApiClient } from './api';
import { Sale } from '@/types/sales';

export async function getSaleById(id: string): Promise<Sale> {
  const session = await getServerSession(authOptions);
  
  if (!session || !session.user) {
    throw new Error('Unauthorized');
  }
  
  try {
    const api = createApiClient(session);
    const response = await api.get<Sale>(`/sales/${id}`);
    return response;
  } catch (error) {
    console.error('Error fetching sale:', error);
    throw new Error('Failed to fetch sale');
  }
} 