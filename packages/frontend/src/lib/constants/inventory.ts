import { StockMovementType, ReferenceType } from '@/types/inventory';

// Stock Movement Type Options
export const STOCK_MOVEMENT_TYPE_OPTIONS = [
  { value: StockMovementType.IN, label: 'Masuk' },
  { value: StockMovementType.OUT, label: 'Keluar' },
  { value: StockMovementType.ADJUSTMENT, label: 'Penyesuaian' },
  { value: StockMovementType.TRANSFER, label: 'Transfer' },
  { value: StockMovementType.ALLOCATION, label: 'Alokasi' },
];

// Status Options
export const INVENTORY_STATUS_OPTIONS = [
  { value: 'all', label: 'Semua Status' },
  { value: 'true', label: 'Aktif' },
  { value: 'false', label: 'Tidak Aktif' },
];

// Filter Options
export const INVENTORY_FILTER_OPTIONS = [
  { value: 'all', label: 'Semua Item' },
  { value: 'lowStock', label: 'Stok Rendah' },
  { value: 'expiringSoon', label: 'Akan <PERSON>' },
];

export const STOCK_STATUS_OPTIONS = [
  { value: 'all', label: 'Semua Stok' },
  { value: 'available', label: 'Tersedia (Stok > 0)' },
  { value: 'low_stock', label: 'Stok Rendah' },
  { value: 'out_of_stock', label: 'Habis (Stok = 0)' },
  { value: 'expired', label: 'Kedaluwarsa' },
  { value: 'expiring_soon', label: 'Akan Kedaluwarsa' },
];

export const EXPIRY_STATUS_OPTIONS = [
  { value: 'all', label: 'Semua' },
  { value: 'valid', label: 'Masih Valid' },
  { value: 'expiring_soon', label: 'Akan Kedaluwarsa (30 hari)' },
  { value: 'expired', label: 'Kedaluwarsa' },
];

// Product Type Options (from backend enum)
export const PRODUCT_TYPE_OPTIONS = [
  { value: 'all', label: 'Semua Jenis' },
  { value: 'MEDICINE', label: 'Obat' },
  { value: 'MEDICAL_DEVICE', label: 'Alat Kesehatan' },
  { value: 'SUPPLEMENT', label: 'Suplemen' },
  { value: 'COSMETIC', label: 'Kosmetik' },
  { value: 'GENERAL', label: 'Umum' },
];

// Product Category Options (from backend enum)
export const PRODUCT_CATEGORY_OPTIONS = [
  { value: 'all', label: 'Semua Kategori' },
  { value: 'PRESCRIPTION', label: 'Resep' },
  { value: 'OTC', label: 'Bebas' },
  { value: 'SUPPLEMENT', label: 'Suplemen' },
  { value: 'MEDICAL_DEVICE', label: 'Alat Kesehatan' },
  { value: 'COSMETIC', label: 'Kosmetik' },
  { value: 'GENERAL', label: 'Umum' },
];

// Search Operator Options
export const SEARCH_OPERATOR_OPTIONS = [
  { value: 'contains', label: 'Mengandung' },
  { value: 'startsWith', label: 'Dimulai dengan' },
  { value: 'endsWith', label: 'Diakhiri dengan' },
  { value: 'equals', label: 'Sama persis' },
];

// Quantity Range Presets
export const QUANTITY_RANGE_PRESETS = [
  { value: 'all', label: 'Semua Jumlah', min: undefined, max: undefined },
  { value: 'low', label: 'Rendah (1-10)', min: 1, max: 10 },
  { value: 'medium', label: 'Sedang (11-50)', min: 11, max: 50 },
  { value: 'high', label: 'Tinggi (51-100)', min: 51, max: 100 },
  { value: 'very_high', label: 'Sangat Tinggi (>100)', min: 101, max: undefined },
];

// Price Range Presets (in IDR)
export const PRICE_RANGE_PRESETS = [
  { value: 'all', label: 'Semua Harga', min: undefined, max: undefined },
  { value: 'low', label: 'Murah (<50K)', min: undefined, max: 50000 },
  { value: 'medium', label: 'Sedang (50K-200K)', min: 50000, max: 200000 },
  { value: 'high', label: 'Mahal (200K-500K)', min: 200000, max: 500000 },
  { value: 'very_high', label: 'Sangat Mahal (>500K)', min: 500000, max: undefined },
];

// Date Range Presets
export const DATE_RANGE_PRESETS = [
  { value: 'all', label: 'Semua Tanggal' },
  { value: 'today', label: 'Hari Ini' },
  { value: 'week', label: '7 Hari Terakhir' },
  { value: 'month', label: '30 Hari Terakhir' },
  { value: 'quarter', label: '3 Bulan Terakhir' },
  { value: 'year', label: '1 Tahun Terakhir' },
  { value: 'custom', label: 'Kustom' },
];

// Sort Options
export const INVENTORY_SORT_OPTIONS = [
  { value: 'createdAt', label: 'Tanggal Dibuat' },
  { value: 'updatedAt', label: 'Tanggal Diperbarui' },
  { value: 'product.name', label: 'Nama Produk' },
  { value: 'quantityOnHand', label: 'Jumlah Stok' },
  { value: 'costPrice', label: 'Harga Beli' },
  { value: 'sellingPrice', label: 'Harga Jual' },
  { value: 'expiryDate', label: 'Tanggal Kedaluwarsa' },
  { value: 'receivedDate', label: 'Tanggal Diterima' },
  { value: 'batchNumber', label: 'Nomor Batch' },
];

export const INVENTORY_TABLE_COLUMNS = [
  { key: 'product', label: 'Produk', sortable: true },
  { key: 'batchNumber', label: 'Batch', sortable: true },
  { key: 'quantityOnHand', label: 'Stok', sortable: true },
  { key: 'unit', label: 'Satuan', sortable: false },
  { key: 'costPrice', label: 'Harga Beli', sortable: true },
  { key: 'sellingPrice', label: 'Harga Jual', sortable: true },
  { key: 'expiryDate', label: 'Kedaluwarsa', sortable: true },
  { key: 'supplier', label: 'Supplier', sortable: false },
  { key: 'location', label: 'Lokasi', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Aksi', sortable: false },
];

// Helper functions for inventory display formatting
export function formatQuantityWithUnit(quantity: number, unitName?: string, unitAbbreviation?: string): string {
  const formattedQuantity = quantity.toLocaleString('id-ID');
  const unit = unitAbbreviation || unitName || 'unit';
  return `${formattedQuantity} ${unit}`;
}

export function formatStockWithAllocation(
  quantityOnHand: number,
  quantityAllocated: number,
  unitName?: string,
  unitAbbreviation?: string
): {
  availableStock: number;
  totalStock: number;
  displayText: string;
  hasAllocation: boolean;
} {
  // NEW IMPLEMENTATION: Use separate quantityAllocated field
  // quantityOnHand = physical stock (total inventory)
  // quantityAllocated = reserved stock
  // availableStock = quantityOnHand - quantityAllocated
  const totalStock = quantityOnHand;
  const availableStock = Math.max(0, quantityOnHand - quantityAllocated);
  const hasAllocation = quantityAllocated > 0;

  if (hasAllocation) {
    const availableText = formatQuantityWithUnit(availableStock, unitName, unitAbbreviation);
    const totalText = formatQuantityWithUnit(totalStock, unitName, unitAbbreviation);
    const allocatedText = formatQuantityWithUnit(quantityAllocated, unitName, unitAbbreviation);

    return {
      availableStock,
      totalStock,
      displayText: `Tersedia: ${availableText} / Total: ${totalText} (${allocatedText} dialokasikan)`,
      hasAllocation: true
    };
  }

  return {
    availableStock: totalStock,
    totalStock,
    displayText: formatQuantityWithUnit(totalStock, unitName, unitAbbreviation),
    hasAllocation: false
  };
}

// Business logic constants for stock allocation behavior
export const STOCK_ALLOCATION_BEHAVIOR = {
  CURRENT: 'IMMEDIATE_DEDUCTION', // Current: allocated stock is immediately deducted
  RECOMMENDED: 'RESERVED_UNTIL_CONSUMED', // Recommended: allocated stock is reserved but still available
} as const;

export const BUSINESS_RULES = {
  // Phase 2 Implementation - FIXED ISSUES
  FIXED_ISSUES: [
    'Allocated stock now tracked separately from consumed stock',
    'POS can sell allocated stock (allocation = planning, not blocking)',
    'Clear distinction between physical stock, allocated stock, and available for new allocations',
    'Proper allocation lifecycle management implemented'
  ],

  // Current allocation behavior (Phase 2)
  ALLOCATION_BEHAVIOR: [
    'Allocation reserves stock for FEFO/FIFO planning purposes',
    'Allocated stock remains physically available and can be sold',
    'Allocation does not block POS sales - only provides planning guidance',
    'Available for new allocations = Physical stock - Already allocated stock',
    'Consumption (actual sales) is separate from allocation (planning)'
  ],

  // Tooltip messaging guidelines
  TOOLTIP_GUIDELINES: [
    'Emphasize allocation is for planning, not sales blocking',
    'Clarify that allocated stock can still be sold through POS',
    'Distinguish between "available for new allocations" vs "available for sales"',
    'Avoid language that suggests allocated stock cannot be used'
  ]
} as const;

// Default pagination
export const DEFAULT_INVENTORY_PAGINATION = {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc' as const,
};

// Stock level thresholds
export const STOCK_THRESHOLDS = {
  LOW_STOCK: 10,
  CRITICAL_STOCK: 5,
  EXPIRING_SOON_DAYS: 30,
  CRITICAL_EXPIRY_DAYS: 7,
};

// Stock Movement Type Labels
export const getStockMovementTypeLabel = (type: StockMovementType): string => {
  const option = STOCK_MOVEMENT_TYPE_OPTIONS.find(opt => opt.value === type);
  return option?.label || type;
};

// Stock Movement Type Colors
export const getStockMovementTypeColor = (type: StockMovementType): string => {
  switch (type) {
    case StockMovementType.IN:
      return 'text-green-600 bg-green-50 border-green-200';
    case StockMovementType.OUT:
      return 'text-red-600 bg-red-50 border-red-200';
    case StockMovementType.ADJUSTMENT:
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case StockMovementType.TRANSFER:
      return 'text-purple-600 bg-purple-50 border-purple-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

// Stock Level Status
export const getStockLevelStatus = (current: number, minimum?: number, maximum?: number) => {
  if (!minimum) return 'normal';
  
  if (current <= 0) return 'out-of-stock';
  if (current <= minimum) return 'low';
  if (maximum && current >= maximum) return 'overstock';
  
  return 'normal';
};

// Stock Level Colors
export const getStockLevelColor = (status: string): string => {
  switch (status) {
    case 'out-of-stock':
      return 'text-red-700 bg-red-100 border-red-300';
    case 'low':
      return 'text-orange-700 bg-orange-100 border-orange-300';
    case 'overstock':
      return 'text-purple-700 bg-purple-100 border-purple-300';
    case 'normal':
    default:
      return 'text-green-700 bg-green-100 border-green-300';
  }
};

// Expiry Status
export const getExpiryStatus = (expiryDate?: string) => {
  if (!expiryDate) return 'no-expiry';
  
  const expiry = new Date(expiryDate);
  const now = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(now.getDate() + 30);
  
  if (expiry < now) return 'expired';
  if (expiry <= thirtyDaysFromNow) return 'expiring-soon';
  
  return 'good';
};

// Expiry Status Colors
export const getExpiryStatusColor = (status: string): string => {
  switch (status) {
    case 'expired':
      return 'text-red-700 bg-red-100 border-red-300';
    case 'expiring-soon':
      return 'text-orange-700 bg-orange-100 border-orange-300';
    case 'good':
      return 'text-green-700 bg-green-100 border-green-300';
    case 'no-expiry':
    default:
      return 'text-gray-700 bg-gray-100 border-gray-300';
  }
};

// Format currency for Indonesian Rupiah
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Format date for Indonesian locale
export const formatDate = (date: string | Date): string => {
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date));
};

// Format date and time for Indonesian locale
export const formatDateTime = (date: string | Date): string => {
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

// Helper functions for date range calculations
export const getDateRangeFromPreset = (preset: string): { from?: string; to?: string } => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (preset) {
    case 'today':
      return {
        from: today.toISOString(),
        to: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1).toISOString(),
      };
    case 'week':
      return {
        from: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      };
    case 'month':
      return {
        from: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      };
    case 'quarter':
      return {
        from: new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      };
    case 'year':
      return {
        from: new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      };
    default:
      return {};
  }
};

// Helper function to format date for input fields
export const formatDateForInput = (date: string | Date): string => {
  return new Date(date).toISOString().split('T')[0];
};

// Helper function to get filter display text
export const getFilterDisplayText = (key: string, value: any): string => {
  switch (key) {
    case 'isActive':
      return value ? 'Aktif' : 'Tidak Aktif';
    case 'lowStock':
      return 'Stok Rendah';
    case 'expiringSoon':
      return 'Akan Kedaluwarsa';
    case 'expired':
      return 'Kedaluwarsa';
    case 'productType':
      return PRODUCT_TYPE_OPTIONS.find(opt => opt.value === value)?.label || value;
    case 'productCategory':
      return PRODUCT_CATEGORY_OPTIONS.find(opt => opt.value === value)?.label || value;
    case 'quantityMin':
      return value === 1 ? 'Tersedia (Stok > 0)' : `Stok Min: ${value}`;
    case 'quantityMax':
      return value === 0 ? 'Habis (Stok = 0)' : `Stok Max: ${value}`;
    case 'costPriceMin':
      return `Harga Beli Min: ${formatCurrency(value)}`;
    case 'costPriceMax':
      return `Harga Beli Max: ${formatCurrency(value)}`;
    case 'sellingPriceMin':
      return `Harga Jual Min: ${formatCurrency(value)}`;
    case 'sellingPriceMax':
      return `Harga Jual Max: ${formatCurrency(value)}`;
    default:
      return String(value);
  }
};

// Reference Type Options
export const REFERENCE_TYPE_OPTIONS = [
  // Sales related
  { value: ReferenceType.SALE, label: 'Penjualan' },
  { value: ReferenceType.SALE_CANCELLATION, label: 'Pembatalan Penjualan' },
  { value: ReferenceType.SALE_REFUND, label: 'Pengembalian Penjualan' },
  { value: ReferenceType.SALE_DELETION, label: 'Penghapusan Penjualan' },
  
  // Purchase related
  { value: ReferenceType.PURCHASE, label: 'Pembelian' },
  { value: ReferenceType.PURCHASE_RETURN, label: 'Retur Pembelian' },
  
  // Inventory management
  { value: ReferenceType.INITIAL_STOCK, label: 'Stok Awal' },
  { value: ReferenceType.ADJUSTMENT, label: 'Penyesuaian' },
  { value: ReferenceType.TRANSFER, label: 'Transfer' },
  
  // Allocation related
  { value: ReferenceType.ALLOCATION, label: 'Alokasi' },
  { value: ReferenceType.DEALLOCATION, label: 'Pembatalan Alokasi' },
  
  // Status changes
  { value: ReferenceType.ACTIVATION, label: 'Aktivasi' },
  { value: ReferenceType.DEACTIVATION, label: 'Deaktivasi' },
  
  // Customer related
  { value: ReferenceType.CUSTOMER_RESERVATION, label: 'Reservasi Pelanggan' },
  { value: ReferenceType.CUSTOMER_ORDER, label: 'Pesanan Pelanggan' },
  
  // Expiry related
  { value: ReferenceType.EXPIRY_WRITE_OFF, label: 'Penghapusan Kedaluwarsa' },
  
  // Other
  { value: ReferenceType.OTHER, label: 'Lainnya' },
];

// Reference Type Labels
export const getReferenceTypeLabel = (type: ReferenceType | string): string => {
  const option = REFERENCE_TYPE_OPTIONS.find(opt => opt.value === type);
  return option?.label || type;
};
