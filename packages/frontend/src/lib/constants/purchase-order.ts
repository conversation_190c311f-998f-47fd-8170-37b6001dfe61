import { PurchaseOrderStatus, PaymentMethod } from '@/types/purchase-order';

// Purchase Order Status Options
export const PURCHASE_ORDER_STATUS_OPTIONS = [
  { value: PurchaseOrderStatus.DRAFT, label: 'Draft' },
  { value: PurchaseOrderStatus.SUBMITTED, label: 'Di<PERSON>ukan' },
  { value: PurchaseOrderStatus.APPROVED, label: '<PERSON>setuju<PERSON>' },
  { value: PurchaseOrderStatus.ORDERED, label: '<PERSON><PERSON>an' },
  { value: PurchaseOrderStatus.PARTIALLY_RECEIVED, label: 'Diter<PERSON> Sebagian' },
  { value: PurchaseOrderStatus.COMPLETED, label: '<PERSON><PERSON><PERSON>' },
  { value: PurchaseOrderStatus.CANCELLED, label: '<PERSON><PERSON><PERSON><PERSON>' },
];

// Payment Method Options
export const PAYMENT_METHOD_OPTIONS = [
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer Bank' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

// Discount Type Options
export const DISCOUNT_TYPE_OPTIONS = [
  { value: 'PERCENTAGE', label: 'Persentase (%)' },
  { value: 'FIXED_AMOUNT', label: 'Nominal Tetap (Rp)' },
];

// Purchase Order Status Colors
export const PURCHASE_ORDER_STATUS_COLORS: Record<PurchaseOrderStatus, string> = {
  [PurchaseOrderStatus.DRAFT]: 'bg-gray-100 text-gray-800 border-gray-200',
  [PurchaseOrderStatus.SUBMITTED]: 'bg-blue-100 text-blue-800 border-blue-200',
  [PurchaseOrderStatus.APPROVED]: 'bg-green-100 text-green-800 border-green-200',
  [PurchaseOrderStatus.ORDERED]: 'bg-purple-100 text-purple-800 border-purple-200',
  [PurchaseOrderStatus.PARTIALLY_RECEIVED]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  [PurchaseOrderStatus.COMPLETED]: 'bg-emerald-100 text-emerald-800 border-emerald-200',
  [PurchaseOrderStatus.CANCELLED]: 'bg-red-100 text-red-800 border-red-200',
};

// Purchase Order Table Columns
export const PURCHASE_ORDER_TABLE_COLUMNS = [
  { key: 'orderNumber', label: 'Nomor PO', sortable: true },
  { key: 'supplier', label: 'Supplier', sortable: true },
  { key: 'orderDate', label: 'Tanggal Order', sortable: true },
  { key: 'expectedDelivery', label: 'Estimasi Pengiriman', sortable: true },
  { key: 'totalAmount', label: 'Total', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'createdAt', label: 'Dibuat', sortable: true },
  { key: 'actions', label: 'Aksi', sortable: false },
];

// Default Pagination
export const DEFAULT_PURCHASE_ORDER_PAGINATION = {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc' as const,
};

// Helper functions
export const getPurchaseOrderStatusLabel = (status: PurchaseOrderStatus): string => {
  return PURCHASE_ORDER_STATUS_OPTIONS.find(option => option.value === status)?.label || status;
};

export const getPurchaseOrderStatusColor = (status: PurchaseOrderStatus): string => {
  return PURCHASE_ORDER_STATUS_COLORS[status] || 'bg-gray-100 text-gray-800 border-gray-200';
};

export const getPaymentMethodLabel = (method: PaymentMethod): string => {
  return PAYMENT_METHOD_OPTIONS.find(option => option.value === method)?.label || method;
};

// Status transition rules
export const PURCHASE_ORDER_STATUS_TRANSITIONS: Record<PurchaseOrderStatus, PurchaseOrderStatus[]> = {
  [PurchaseOrderStatus.DRAFT]: [PurchaseOrderStatus.SUBMITTED, PurchaseOrderStatus.CANCELLED],
  [PurchaseOrderStatus.SUBMITTED]: [PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.CANCELLED],
  [PurchaseOrderStatus.APPROVED]: [PurchaseOrderStatus.ORDERED, PurchaseOrderStatus.CANCELLED],
  [PurchaseOrderStatus.ORDERED]: [PurchaseOrderStatus.PARTIALLY_RECEIVED, PurchaseOrderStatus.COMPLETED, PurchaseOrderStatus.CANCELLED],
  [PurchaseOrderStatus.PARTIALLY_RECEIVED]: [PurchaseOrderStatus.COMPLETED, PurchaseOrderStatus.CANCELLED],
  [PurchaseOrderStatus.COMPLETED]: [], // Final state
  [PurchaseOrderStatus.CANCELLED]: [], // Final state
};

// Check if status transition is allowed
export const isStatusTransitionAllowed = (currentStatus: PurchaseOrderStatus, newStatus: PurchaseOrderStatus): boolean => {
  return PURCHASE_ORDER_STATUS_TRANSITIONS[currentStatus]?.includes(newStatus) || false;
};

// Get allowed next statuses
export const getAllowedNextStatuses = (currentStatus: PurchaseOrderStatus): PurchaseOrderStatus[] => {
  return PURCHASE_ORDER_STATUS_TRANSITIONS[currentStatus] || [];
};

// Purchase Order Form Validation Rules
export const PURCHASE_ORDER_VALIDATION = {
  orderNumber: {
    required: true,
    minLength: 3,
    maxLength: 50,
  },
  supplierId: {
    required: true,
  },
  orderDate: {
    required: true,
  },
  items: {
    required: true,
    minItems: 1,
  },
  itemQuantity: {
    required: true,
    min: 1,
  },
  itemUnitPrice: {
    required: true,
    min: 0,
  },
  paymentTerms: {
    min: 0,
    max: 365,
  },
  discountValue: {
    min: 0,
  },
};

// Default form values
export const DEFAULT_PURCHASE_ORDER_FORM = {
  supplierId: '',
  orderDate: new Date().toISOString().split('T')[0],
  expectedDelivery: '',
  discountType: undefined,
  discountValue: undefined,
  paymentTerms: 30,
  paymentMethod: PaymentMethod.TRANSFER,
  deliveryAddress: '',
  deliveryContact: '',
  deliveryPhone: '',
  deliveryNotes: '',
  notes: '',
  internalNotes: '',
  items: [],
};

// Tax calculation constants (now handled by dynamic tax service)
// Note: Tax rates are now fetched from backend tax configuration service
export const TAX_INCLUSIVE = false; // Default to tax exclusive pricing
