import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { TaxConfigurationService } from './tax-configuration.service';
import { TaxType, TaxEntityType } from '../dto/tax-configuration.dto';

/**
 * Service for Indonesian tax compliance and calculations
 * Handles multiple tax types: PPN, PPh 21, PPh 23, PPh 25, PP 23, etc.
 */
@Injectable()
export class IndonesianTaxService {
  private readonly logger = new Logger(IndonesianTaxService.name);

  // Indonesian tax thresholds and rates (as of 2024)
  private readonly TAX_THRESHOLDS = {
    PKP_REGISTRATION: 4_800_000_000, // Rp 4.8 billion for PKP registration
    PP_23_MIN: 500_000_000,          // Rp 500 million minimum for PP 23
    PP_23_MAX: 4_800_000_000,        // Rp 4.8 billion maximum for PP 23
    PPH_21_PTKP: 60_000_000,         // Rp 60 million PTKP threshold
  };

  private readonly TAX_RATES = {
    PPN: 11,           // 11% (will be 12% from 2025)
    PP_23: 0.5,        // 0.5% of revenue
    PPH_25_INDIVIDUAL: 0.75, // 0.75% of revenue for individuals
    PPH_25_CORPORATE: 22,    // 22% of net profit for corporations
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly taxConfigService: TaxConfigurationService,
  ) { }

  /**
   * Determine applicable tax types based on business characteristics
   */
  async getApplicableTaxTypes(
    entityType: TaxEntityType,
    annualRevenue: number,
    yearsInOperation: number = 1,
    hasEmployees: boolean = false
  ): Promise<TaxType[]> {
    const applicableTaxes: TaxType[] = [];

    // PPN - applicable if registered as PKP
    if (annualRevenue >= this.TAX_THRESHOLDS.PKP_REGISTRATION) {
      applicableTaxes.push(TaxType.PPN);
    }

    // Income tax determination
    if (entityType === TaxEntityType.INDIVIDUAL) {
      // Individual taxpayer
      if (annualRevenue >= this.TAX_THRESHOLDS.PP_23_MIN &&
        annualRevenue <= this.TAX_THRESHOLDS.PP_23_MAX &&
        yearsInOperation <= 7) {
        applicableTaxes.push(TaxType.PP_23);
      } else {
        applicableTaxes.push(TaxType.PPH_25);
      }
    } else {
      // Corporate taxpayer
      if (yearsInOperation <= 3 &&
        annualRevenue <= this.TAX_THRESHOLDS.PP_23_MAX) {
        applicableTaxes.push(TaxType.PP_23);
      } else {
        applicableTaxes.push(TaxType.PPH_25);
      }
    }

    // Employee tax (only applicable if pharmacy has employees)
    if (hasEmployees) {
      applicableTaxes.push(TaxType.PPH_21);
    }

    return applicableTaxes;
  }

  /**
   * Calculate PP 23 tax (Final Income Tax for MSMEs)
   */
  async calculatePP23Tax(
    monthlyRevenue: number,
    entityType: TaxEntityType,
    annualRevenue: number
  ): Promise<{
    taxableRevenue: number;
    taxAmount: number;
    taxRate: number;
  }> {
    let taxableRevenue = monthlyRevenue;

    // For individuals, deduct non-taxable portion
    if (entityType === TaxEntityType.INDIVIDUAL) {
      const nonTaxableMonthly = this.TAX_THRESHOLDS.PP_23_MIN / 12;
      taxableRevenue = Math.max(0, monthlyRevenue - nonTaxableMonthly);
    }

    const taxAmount = taxableRevenue * (this.TAX_RATES.PP_23 / 100);

    return {
      taxableRevenue,
      taxAmount,
      taxRate: this.TAX_RATES.PP_23,
    };
  }

  /**
   * Calculate PPh 25 tax (Income Tax Installments)
   */
  async calculatePPh25Tax(
    monthlyRevenue: number,
    entityType: TaxEntityType,
    previousYearTaxDue?: number
  ): Promise<{
    monthlyInstallment: number;
    taxRate: number;
    calculationBasis: string;
  }> {
    if (entityType === TaxEntityType.INDIVIDUAL) {
      // Individual: 0.75% of monthly revenue
      const monthlyInstallment = monthlyRevenue * (this.TAX_RATES.PPH_25_INDIVIDUAL / 100);
      return {
        monthlyInstallment,
        taxRate: this.TAX_RATES.PPH_25_INDIVIDUAL,
        calculationBasis: 'Monthly Revenue',
      };
    } else {
      // Corporate: 1/12 of previous year's tax due
      if (previousYearTaxDue === undefined || previousYearTaxDue === null) {
        throw new Error('Data pajak terutang tahun sebelumnya diperlukan untuk kalkulasi PPh 25 badan usaha');
      }

      const monthlyInstallment = previousYearTaxDue / 12;
      return {
        monthlyInstallment,
        taxRate: this.TAX_RATES.PPH_25_CORPORATE,
        calculationBasis: 'Previous Year Tax Due',
      };
    }
  }

  /**
   * Calculate PPN with Indonesian-specific rules
   */
  async calculateIndonesianPPN(
    subtotal: number,
    isRetailTransaction: boolean = true
  ): Promise<{
    subtotal: number;
    ppnAmount: number;
    totalAmount: number;
    ppnRate: number;
    requiresFakturPajak: boolean;
  }> {
    const ppnConfig = await this.taxConfigService.getCurrentPPNConfiguration();

    if (!ppnConfig.isActive) {
      return {
        subtotal,
        ppnAmount: 0,
        totalAmount: subtotal,
        ppnRate: 0,
        requiresFakturPajak: false,
      };
    }

    const ppnRate = ppnConfig.taxRate;
    const ppnAmount = subtotal * (ppnRate / 100);
    const totalAmount = subtotal + ppnAmount;

    return {
      subtotal,
      ppnAmount,
      totalAmount,
      ppnRate,
      requiresFakturPajak: true, // Pharmacies can issue simplified tax invoices
    };
  }

  /**
   * Get tax compliance status for a pharmacy
   */
  async getTaxComplianceStatus(
    entityType: TaxEntityType,
    annualRevenue: number,
    yearsInOperation: number,
    hasEmployees: boolean
  ): Promise<{
    requiredRegistrations: string[];
    applicableTaxes: TaxType[];
    complianceChecklist: Array<{
      requirement: string;
      status: 'required' | 'optional' | 'not_applicable';
      description: string;
    }>;
  }> {
    const applicableTaxes = await this.getApplicableTaxTypes(
      entityType,
      annualRevenue,
      yearsInOperation,
      hasEmployees
    );

    const requiredRegistrations: string[] = [];
    const complianceChecklist: Array<{
      requirement: string;
      status: 'required' | 'optional' | 'not_applicable';
      description: string;
    }> = [];

    // NPWP requirement
    requiredRegistrations.push('NPWP (Nomor Pokok Wajib Pajak)');
    complianceChecklist.push({
      requirement: 'Pendaftaran NPWP',
      status: 'required' as const,
      description: 'Semua usaha apotek wajib memiliki NPWP',
    });

    // PKP registration
    if (annualRevenue >= this.TAX_THRESHOLDS.PKP_REGISTRATION) {
      requiredRegistrations.push('PKP (Pengusaha Kena Pajak)');
      complianceChecklist.push({
        requirement: 'Pendaftaran PKP',
        status: 'required' as const,
        description: 'Wajib untuk usaha dengan omzet tahunan ≥ Rp 4,8 miliar',
      });
    }

    // e-Faktur application
    if (applicableTaxes.includes(TaxType.PPN)) {
      complianceChecklist.push({
        requirement: 'Aplikasi e-Faktur',
        status: 'required' as const,
        description: 'Diperlukan untuk pelaporan PPN dan penerbitan faktur pajak',
      });
    }

    // Employee tax obligations
    if (hasEmployees) {
      complianceChecklist.push({
        requirement: 'Pemotongan PPh 21',
        status: 'required' as const,
        description: 'Memotong dan melaporkan pajak penghasilan karyawan',
      });
    }

    return {
      requiredRegistrations,
      applicableTaxes,
      complianceChecklist,
    };
  }

  /**
   * Get tax calendar for pharmacy compliance
   */
  getTaxCalendar(): Array<{
    taxType: TaxType;
    deadline: string;
    description: string;
    frequency: string;
  }> {
    return [
      {
        taxType: TaxType.PPH_21,
        deadline: 'Tanggal 10 bulan berikutnya',
        description: 'Batas waktu pembayaran PPh 21',
        frequency: 'Bulanan',
      },
      {
        taxType: TaxType.PPH_21,
        deadline: 'Tanggal 20 bulan berikutnya',
        description: 'Batas waktu pelaporan SPT PPh 21',
        frequency: 'Bulanan',
      },
      {
        taxType: TaxType.PP_23,
        deadline: 'Tanggal 15 bulan berikutnya',
        description: 'Batas waktu pembayaran PP 23',
        frequency: 'Bulanan',
      },
      {
        taxType: TaxType.PPH_25,
        deadline: 'Tanggal 15 bulan berikutnya',
        description: 'Batas waktu pembayaran angsuran PPh 25',
        frequency: 'Bulanan',
      },
      {
        taxType: TaxType.PPN,
        deadline: 'Akhir bulan berikutnya',
        description: 'Batas waktu pembayaran dan pelaporan SPT PPN',
        frequency: 'Bulanan',
      },
    ];
  }

  /**
   * Validate tax configuration against Indonesian regulations
   */
  async validateTaxConfiguration(
    taxType: TaxType,
    taxRate: number,
    entityType?: TaxEntityType
  ): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    switch (taxType) {
      case TaxType.PPN:
        if (taxRate !== 11 && taxRate !== 12) {
          errors.push('Tarif PPN harus 11% (saat ini) atau 12% (mulai 2025)');
        }
        break;

      case TaxType.PP_23:
        if (taxRate !== 0.5) {
          errors.push('Tarif PP 23 harus 0,5% dari omzet');
        }
        break;

      case TaxType.PPH_25:
        if (entityType === TaxEntityType.INDIVIDUAL && taxRate !== 0.75) {
          errors.push('Tarif PPh 25 untuk perorangan harus 0,75% dari omzet');
        } else if (entityType === TaxEntityType.CORPORATE && taxRate !== 22) {
          errors.push('Tarif PPh 25 untuk badan usaha harus 22% dari laba bersih');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
