import { Injectable, Logger } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { TaxConfigurationService } from './tax-configuration.service';
import { TaxCalculationOptions, TaxCalculationResult } from '../interfaces/procurement.interface';
import { TaxType } from '../dto/tax-configuration.dto';

/**
 * Service for calculating taxes on procurement transactions
 * Supports both inclusive and exclusive PPN calculations with proper Prisma Decimal handling
 */
@Injectable()
export class TaxCalculationService {
  private readonly logger = new Logger(TaxCalculationService.name);

  constructor(private readonly taxConfigService: TaxConfigurationService) { }

  /**
   * Calculate PPN tax for procurement transactions
   * @param subtotal - The subtotal amount before tax (as Prisma.Decimal)
   * @param options - Tax calculation options
   * @returns Tax calculation result with all amounts as numbers for easier handling
   */
  async calculatePPN(
    subtotal: Prisma.Decimal,
    options: TaxCalculationOptions = {}
  ): Promise<TaxCalculationResult> {
    try {
      // Validate inputs
      this.validateInputs(subtotal);

      const {
        taxType = TaxType.PPN,
        isInclusive = false,
        exemptItems = [],
      } = options;

      // Check if PPN is active
      const isPPNActive = await this.taxConfigService.isPPNActive();
      if (!isPPNActive) {
        return this.createZeroTaxResult(subtotal, taxType, isInclusive);
      }

      // Get current PPN rate
      const taxRate = await this.taxConfigService.getEffectivePPNRate();
      if (taxRate === 0) {
        return this.createZeroTaxResult(subtotal, taxType, isInclusive);
      }

      // Convert subtotal to number for calculations
      const subtotalAmount = Number(subtotal);

      let taxAmount: number;
      let totalAmount: number;
      let finalSubtotal: number;

      if (isInclusive) {
        // Tax is included in the subtotal
        // Formula: taxAmount = subtotal * (taxRate / (100 + taxRate))
        // finalSubtotal = subtotal - taxAmount
        taxAmount = subtotalAmount * (taxRate / (100 + taxRate));
        finalSubtotal = subtotalAmount - taxAmount;
        totalAmount = subtotalAmount;
      } else {
        // Tax is added to the subtotal
        // Formula: taxAmount = subtotal * (taxRate / 100)
        // totalAmount = subtotal + taxAmount
        finalSubtotal = subtotalAmount;
        taxAmount = subtotalAmount * (taxRate / 100);
        totalAmount = subtotalAmount + taxAmount;
      }

      // Round to 2 decimal places for currency precision
      taxAmount = Math.round(taxAmount * 100) / 100;
      totalAmount = Math.round(totalAmount * 100) / 100;
      finalSubtotal = Math.round(finalSubtotal * 100) / 100;

      this.logger.debug(`PPN calculation: subtotal=${finalSubtotal}, tax=${taxAmount}, total=${totalAmount}, rate=${taxRate}%, inclusive=${isInclusive}`);

      return {
        subtotal: finalSubtotal,
        taxAmount,
        totalAmount,
        taxRate,
        taxType,
        isInclusive,
      };
    } catch (error) {
      this.logger.error('Failed to calculate PPN', error);
      // Return zero tax result as fallback
      return this.createZeroTaxResult(subtotal, options.taxType || TaxType.PPN, options.isInclusive || false);
    }
  }

  /**
   * Calculate tax for multiple line items with different tax treatments
   * @param items - Array of items with subtotal and tax options
   * @returns Aggregated tax calculation result
   */
  async calculateMultipleItems(
    items: Array<{
      subtotal: Prisma.Decimal;
      options?: TaxCalculationOptions;
    }>
  ): Promise<TaxCalculationResult> {
    try {
      // Validate all items first
      for (const item of items) {
        this.validateInputs(item.subtotal);
      }

      let totalSubtotal = 0;
      let totalTaxAmount = 0;
      let totalAmount = 0;
      let effectiveTaxRate = 0;
      let taxType = TaxType.PPN;
      let isInclusive = false;

      for (const item of items) {
        const result = await this.calculatePPN(item.subtotal, item.options);

        totalSubtotal += result.subtotal;
        totalTaxAmount += result.taxAmount;
        totalAmount += result.totalAmount;

        // Use the first item's tax settings as the overall settings
        if (items.indexOf(item) === 0) {
          effectiveTaxRate = result.taxRate;
          taxType = result.taxType;
          isInclusive = result.isInclusive;
        }
      }

      // Round final amounts
      totalSubtotal = Math.round(totalSubtotal * 100) / 100;
      totalTaxAmount = Math.round(totalTaxAmount * 100) / 100;
      totalAmount = Math.round(totalAmount * 100) / 100;

      return {
        subtotal: totalSubtotal,
        taxAmount: totalTaxAmount,
        totalAmount,
        taxRate: effectiveTaxRate,
        taxType,
        isInclusive,
      };
    } catch (error) {
      this.logger.error('Failed to calculate multiple items tax', error);
      throw error;
    }
  }

  /**
   * Calculate tax with discount applied
   * @param subtotal - Original subtotal before discount
   * @param discountAmount - Discount amount to apply
   * @param options - Tax calculation options
   * @returns Tax calculation result after discount
   */
  async calculateWithDiscount(
    subtotal: Prisma.Decimal,
    discountAmount: Prisma.Decimal,
    options: TaxCalculationOptions = {}
  ): Promise<TaxCalculationResult> {
    try {
      // Validate inputs
      this.validateInputs(subtotal);
      if (discountAmount.lt(0)) {
        throw new Error('Jumlah diskon tidak boleh negatif');
      }

      // Apply discount first, then calculate tax
      const afterDiscount = subtotal.sub(discountAmount);

      // Ensure we don't have negative amounts
      const finalSubtotal = afterDiscount.gte(0) ? afterDiscount : new Prisma.Decimal(0);

      return this.calculatePPN(finalSubtotal, options);
    } catch (error) {
      this.logger.error('Failed to calculate tax with discount', error);
      throw error;
    }
  }

  /**
   * Get tax breakdown for display purposes
   * @param subtotal - Subtotal amount
   * @param options - Tax calculation options
   * @returns Detailed tax breakdown
   */
  async getTaxBreakdown(
    subtotal: Prisma.Decimal,
    options: TaxCalculationOptions = {}
  ): Promise<{
    subtotal: number;
    taxRate: number;
    taxAmount: number;
    totalAmount: number;
    taxDescription: string;
    isInclusive: boolean;
  }> {
    // Validate inputs
    this.validateInputs(subtotal);

    const result = await this.calculatePPN(subtotal, options);
    const config = await this.taxConfigService.getCurrentPPNConfiguration();

    return {
      subtotal: result.subtotal,
      taxRate: result.taxRate,
      taxAmount: result.taxAmount,
      totalAmount: result.totalAmount,
      taxDescription: config.description || 'Pajak Pertambahan Nilai (PPN)',
      isInclusive: result.isInclusive,
    };
  }

  /**
   * Create a zero tax result for cases where tax doesn't apply
   */
  private createZeroTaxResult(
    subtotal: Prisma.Decimal,
    taxType: TaxType,
    isInclusive: boolean
  ): TaxCalculationResult {
    const subtotalAmount = Number(subtotal);

    return {
      subtotal: subtotalAmount,
      taxAmount: 0,
      totalAmount: subtotalAmount,
      taxRate: 0,
      taxType,
      isInclusive,
    };
  }

  /**
   * Validate tax calculation inputs
   */
  private validateInputs(subtotal: Prisma.Decimal): void {
    if (subtotal.lt(0)) {
      throw new Error('Subtotal tidak boleh negatif');
    }
  }

  /**
   * Convert Prisma Decimal to number safely
   */
  private toNumber(decimal: Prisma.Decimal): number {
    return Number(decimal.toFixed(2));
  }
}
