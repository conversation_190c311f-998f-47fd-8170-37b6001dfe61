import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { InventoryService } from '../../inventory/inventory.service';
import { UnitConversionService } from '../../common/services/unit-conversion.service';
import { CreateGoodsReceiptDto } from '../dto/create-goods-receipt.dto';
import { UpdateGoodsReceiptDto } from '../dto/update-goods-receipt.dto';
import { GoodsReceiptQueryDto, QualityControlUpdateDto, GoodsReceiptStatusUpdateDto } from '../dto/goods-receipt-query.dto';
import { NumberGeneratorService } from '../utils/number-generator.utils';
import { ProcurementValidationUtils } from '../utils/procurement-validation.utils';
import { <PERSON>risma, GoodsReceiptStatus, QualityControlStatus, PurchaseOrderStatus, PurchaseOrderItemStatus, StockMovementType, ReferenceType } from '@prisma/client';

@Injectable()
export class GoodsReceiptService {
  constructor(
    private prisma: PrismaService,
    private numberGeneratorService: NumberGeneratorService,
    private inventoryService: InventoryService,
    private unitConversionService: UnitConversionService,
  ) { }

  async create(createGoodsReceiptDto: CreateGoodsReceiptDto, userId: string) {
    // Validate supplier exists
    const supplier = await this.prisma.supplier.findUnique({
      where: { id: createGoodsReceiptDto.supplierId },
    });

    if (!supplier) {
      throw new BadRequestException('Supplier tidak ditemukan');
    }

    // Validate purchase order if provided
    let purchaseOrder: any = null;
    if (createGoodsReceiptDto.purchaseOrderId) {
      purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id: createGoodsReceiptDto.purchaseOrderId },
        include: { items: true },
      });

      if (!purchaseOrder) {
        throw new BadRequestException('Purchase order tidak ditemukan');
      }

      if (purchaseOrder.supplierId !== createGoodsReceiptDto.supplierId) {
        throw new BadRequestException('Supplier pada goods receipt harus sama dengan purchase order');
      }
    }

    // Generate receipt number if not provided
    const receiptNumber = createGoodsReceiptDto.receiptNumber ||
      await this.numberGeneratorService.generateGoodsReceiptNumber();

    // Check if receipt number already exists
    const existingReceipt = await this.prisma.goodsReceipt.findUnique({
      where: { receiptNumber },
    });

    if (existingReceipt) {
      throw new ConflictException('Nomor goods receipt sudah digunakan');
    }

    // Validate and prepare items
    const { items, ...receiptData } = createGoodsReceiptDto;

    if (!items || items.length === 0) {
      throw new BadRequestException('Goods receipt harus memiliki minimal satu item');
    }

    // Validate products and units exist
    for (const item of items) {
      const product = await this.prisma.product.findUnique({
        where: { id: item.productId },
      });

      if (!product) {
        throw new BadRequestException(`Produk dengan ID ${item.productId} tidak ditemukan`);
      }

      const unit = await this.prisma.productUnit.findUnique({
        where: { id: item.unitId },
      });

      if (!unit) {
        throw new BadRequestException(`Unit dengan ID ${item.unitId} tidak ditemukan`);
      }

      // Validate expiry date if provided
      if (item.expiryDate) {
        ProcurementValidationUtils.validateExpiryDate(
          new Date(item.expiryDate),
          item.manufacturingDate ? new Date(item.manufacturingDate) : undefined
        );
      }

      // Validate quantities
      ProcurementValidationUtils.validateGoodsReceiptQuantities(
        item.quantityReceived,
        0, // quantityAccepted - will be set during quality control
        0  // quantityRejected - will be set during quality control
      );
    }

    try {
      const goodsReceipt = await this.prisma.$transaction(async (prisma) => {
        // Calculate total amount
        let totalAmount = new Prisma.Decimal(0);
        const processedItems = items.map(item => {
          const itemTotal = new Prisma.Decimal(item.unitPrice).mul(item.quantityReceived);
          totalAmount = totalAmount.add(itemTotal);

          return {
            ...item,
            totalPrice: itemTotal,
            unitPrice: new Prisma.Decimal(item.unitPrice),
            quantityAccepted: 0, // Will be set during quality control
            quantityRejected: 0, // Will be set during quality control
          };
        });

        // Create goods receipt
        const receipt = await prisma.goodsReceipt.create({
          data: {
            receiptNumber,
            purchaseOrderId: createGoodsReceiptDto.purchaseOrderId,
            supplierId: createGoodsReceiptDto.supplierId,
            status: createGoodsReceiptDto.status || GoodsReceiptStatus.PENDING,
            receiptDate: createGoodsReceiptDto.receiptDate ? new Date(createGoodsReceiptDto.receiptDate) : new Date(),
            deliveryDate: createGoodsReceiptDto.deliveryDate ? new Date(createGoodsReceiptDto.deliveryDate) : null,
            invoiceNumber: receiptData.invoiceNumber,
            deliveryNote: receiptData.deliveryNote,
            inspectionDate: receiptData.inspectionDate ? new Date(receiptData.inspectionDate) : null,
            inspectionBy: receiptData.inspectionBy,
            qualityStatus: receiptData.qualityStatus || QualityControlStatus.PENDING,
            qualityNotes: receiptData.qualityNotes,
            deliveredBy: receiptData.deliveredBy,
            receivedBy: userId,
            deliveryCondition: receiptData.deliveryCondition,
            totalAmount,
            notes: receiptData.notes,
            internalNotes: receiptData.internalNotes,
            createdBy: userId,
            updatedBy: userId,
          },
        });

        // Create goods receipt items
        await prisma.goodsReceiptItem.createMany({
          data: processedItems.map(item => ({
            goodsReceiptId: receipt.id,
            purchaseOrderItemId: item.purchaseOrderItemId,
            productId: item.productId,
            unitId: item.unitId,
            quantityOrdered: item.quantityOrdered,
            quantityReceived: item.quantityReceived,
            quantityAccepted: item.quantityAccepted,
            quantityRejected: item.quantityRejected,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            batchNumber: item.batchNumber,
            expiryDate: item.expiryDate ? new Date(item.expiryDate) : null,
            manufacturingDate: item.manufacturingDate ? new Date(item.manufacturingDate) : null,
            qualityStatus: item.qualityStatus || QualityControlStatus.PENDING,
            qualityNotes: item.qualityNotes,
            inspectionDate: item.inspectionDate ? new Date(item.inspectionDate) : null,
            inspectionBy: item.inspectionBy,
            storageLocation: item.storageLocation,
            storageCondition: item.storageCondition,
            conditionOnReceipt: item.conditionOnReceipt,
            damageNotes: item.damageNotes,
            notes: item.notes,
          })),
        });

        // Update purchase order item quantities if linked to PO
        if (createGoodsReceiptDto.purchaseOrderId) {
          for (const item of processedItems) {
            if (item.purchaseOrderItemId) {
              const poItem = await prisma.purchaseOrderItem.findUnique({
                where: { id: item.purchaseOrderItemId },
              });

              if (poItem) {
                const newQuantityReceived = poItem.quantityReceived + item.quantityReceived;
                let newStatus = poItem.status;

                if (newQuantityReceived >= poItem.quantityOrdered) {
                  newStatus = PurchaseOrderItemStatus.FULLY_RECEIVED;
                } else if (newQuantityReceived > 0) {
                  newStatus = PurchaseOrderItemStatus.PARTIALLY_RECEIVED;
                }

                await prisma.purchaseOrderItem.update({
                  where: { id: item.purchaseOrderItemId },
                  data: {
                    quantityReceived: newQuantityReceived,
                    status: newStatus,
                    actualDelivery: new Date(),
                  },
                });
              }
            }
          }

          // Update purchase order status
          const allItems = await prisma.purchaseOrderItem.findMany({
            where: { purchaseOrderId: createGoodsReceiptDto.purchaseOrderId },
          });

          const allFullyReceived = allItems.every(item =>
            item.status === PurchaseOrderItemStatus.FULLY_RECEIVED
          );
          const anyPartiallyReceived = allItems.some(item =>
            item.status === PurchaseOrderItemStatus.PARTIALLY_RECEIVED ||
            item.status === PurchaseOrderItemStatus.FULLY_RECEIVED
          );

          let newPOStatus = purchaseOrder!.status;
          if (allFullyReceived) {
            newPOStatus = PurchaseOrderStatus.COMPLETED;
          } else if (anyPartiallyReceived) {
            newPOStatus = PurchaseOrderStatus.PARTIALLY_RECEIVED;
          }

          if (newPOStatus !== purchaseOrder!.status) {
            await prisma.purchaseOrder.update({
              where: { id: createGoodsReceiptDto.purchaseOrderId },
              data: {
                status: newPOStatus,
                actualDelivery: new Date(),
                updatedBy: userId,
              },
            });
          }
        }

        return receipt;
      });

      return this.findOne(goodsReceipt.id);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Nomor goods receipt sudah digunakan');
        }
      }
      throw error;
    }
  }

  async findAll(query: GoodsReceiptQueryDto) {
    const {
      page = 1,
      limit = 10,
      search,
      supplierId,
      purchaseOrderId,
      status,
      qualityStatus,
      receiptDateFrom,
      receiptDateTo,
      deliveryDateFrom,
      deliveryDateTo,
      inspectionBy,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.GoodsReceiptWhereInput = {};

    if (search) {
      where.OR = [
        { receiptNumber: { contains: search, mode: 'insensitive' } },
        { invoiceNumber: { contains: search, mode: 'insensitive' } },
        { deliveryNote: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
        { purchaseOrder: { orderNumber: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (supplierId) {
      where.supplierId = supplierId;
    }

    if (purchaseOrderId) {
      where.purchaseOrderId = purchaseOrderId;
    }

    if (status) {
      where.status = status as GoodsReceiptStatus;
    }

    if (qualityStatus) {
      where.qualityStatus = qualityStatus as QualityControlStatus;
    }

    if (receiptDateFrom || receiptDateTo) {
      where.receiptDate = {};
      if (receiptDateFrom) {
        where.receiptDate.gte = new Date(receiptDateFrom);
      }
      if (receiptDateTo) {
        where.receiptDate.lte = new Date(receiptDateTo);
      }
    }

    if (deliveryDateFrom || deliveryDateTo) {
      where.deliveryDate = {};
      if (deliveryDateFrom) {
        where.deliveryDate.gte = new Date(deliveryDateFrom);
      }
      if (deliveryDateTo) {
        where.deliveryDate.lte = new Date(deliveryDateTo);
      }
    }

    if (inspectionBy) {
      where.inspectionBy = inspectionBy;
    }

    // Build order by
    const orderBy: Prisma.GoodsReceiptOrderByWithRelationInput = {};
    if (sortBy === 'supplier') {
      orderBy.supplier = { name: sortOrder };
    } else if (sortBy === 'purchaseOrder') {
      orderBy.purchaseOrder = { orderNumber: sortOrder };
    } else if (sortBy === 'totalAmount') {
      orderBy.totalAmount = sortOrder;
    } else {
      orderBy[sortBy as keyof Prisma.GoodsReceiptOrderByWithRelationInput] = sortOrder;
    }

    const [goodsReceipts, total] = await Promise.all([
      this.prisma.goodsReceipt.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          supplier: {
            select: {
              id: true,
              code: true,
              name: true,
              type: true,
              status: true,
            },
          },
          purchaseOrder: {
            select: {
              id: true,
              orderNumber: true,
              status: true,
              orderDate: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          inspectionByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          receivedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              items: true,
            },
          },
        },
      }),
      this.prisma.goodsReceipt.count({ where }),
    ]);

    return {
      data: goodsReceipts,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string) {
    const goodsReceipt = await this.prisma.goodsReceipt.findUnique({
      where: { id },
      include: {
        supplier: {
          include: {
            contacts: {
              where: { isPrimary: true },
              take: 1,
            },
          },
        },
        purchaseOrder: {
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    code: true,
                    name: true,
                  },
                },
                unit: {
                  select: {
                    id: true,
                    name: true,
                    abbreviation: true,
                  },
                },
              },
            },
          },
        },
        items: {
          include: {
            product: {
              include: {
                baseUnit: true,
              },
            },
            unit: true,
            purchaseOrderItem: {
              select: {
                id: true,
                quantityOrdered: true,
                quantityReceived: true,
                unitPrice: true,
              },
            },
            inspectionByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        inspectionByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!goodsReceipt) {
      throw new NotFoundException('Goods receipt tidak ditemukan');
    }

    return goodsReceipt;
  }

  async update(id: string, updateGoodsReceiptDto: UpdateGoodsReceiptDto, userId: string) {
    const existingReceipt = await this.findOne(id);

    // Check if receipt can be updated based on status
    if (existingReceipt.status === GoodsReceiptStatus.COMPLETED) {
      throw new BadRequestException('Goods receipt yang sudah selesai tidak dapat diubah');
    }

    try {
      const updatedReceipt = await this.prisma.$transaction(async (prisma) => {
        // Prepare update data without items
        const { items, ...receiptUpdateData } = updateGoodsReceiptDto;

        // Update goods receipt
        await prisma.goodsReceipt.update({
          where: { id },
          data: {
            ...receiptUpdateData,
            receiptDate: updateGoodsReceiptDto.receiptDate ?
              new Date(updateGoodsReceiptDto.receiptDate) : undefined,
            deliveryDate: updateGoodsReceiptDto.deliveryDate ?
              new Date(updateGoodsReceiptDto.deliveryDate) : undefined,
            inspectionDate: updateGoodsReceiptDto.inspectionDate ?
              new Date(updateGoodsReceiptDto.inspectionDate) : undefined,
            updatedBy: userId,
          },
        });

        // Handle items update if provided
        if (updateGoodsReceiptDto.items) {
          // Delete existing items
          await prisma.goodsReceiptItem.deleteMany({
            where: { goodsReceiptId: id },
          });

          // Create new items
          let totalAmount = new Prisma.Decimal(0);

          await prisma.goodsReceiptItem.createMany({
            data: updateGoodsReceiptDto.items.map(item => {
              const itemTotal = new Prisma.Decimal(item.unitPrice).mul(item.quantityReceived);
              totalAmount = totalAmount.add(itemTotal);

              return {
                goodsReceiptId: id,
                purchaseOrderItemId: item.purchaseOrderItemId,
                productId: item.productId,
                unitId: item.unitId,
                quantityOrdered: item.quantityOrdered,
                quantityReceived: item.quantityReceived,
                quantityAccepted: 0, // Will be set during quality control
                quantityRejected: 0, // Will be set during quality control
                unitPrice: new Prisma.Decimal(item.unitPrice),
                totalPrice: itemTotal,
                batchNumber: item.batchNumber,
                expiryDate: item.expiryDate ? new Date(item.expiryDate) : null,
                manufacturingDate: item.manufacturingDate ? new Date(item.manufacturingDate) : null,
                qualityStatus: item.qualityStatus || QualityControlStatus.PENDING,
                qualityNotes: item.qualityNotes,
                inspectionDate: item.inspectionDate ? new Date(item.inspectionDate) : null,
                inspectionBy: item.inspectionBy,
                storageLocation: item.storageLocation,
                storageCondition: item.storageCondition,
                conditionOnReceipt: item.conditionOnReceipt,
                damageNotes: item.damageNotes,
                notes: item.notes,
              };
            }),
          });

          // Update total amount
          await prisma.goodsReceipt.update({
            where: { id },
            data: { totalAmount },
          });

          // Update purchase order items if this goods receipt is linked to a purchase order
          const goodsReceipt = await prisma.goodsReceipt.findUnique({
            where: { id },
            select: { purchaseOrderId: true },
          });

          if (goodsReceipt?.purchaseOrderId) {
            // Get all goods receipt items for this purchase order to calculate total quantities
            const allGoodsReceiptItems = await prisma.goodsReceiptItem.findMany({
              where: {
                goodsReceipt: { purchaseOrderId: goodsReceipt.purchaseOrderId },
                purchaseOrderItemId: { not: null },
              },
              select: {
                purchaseOrderItemId: true,
                quantityReceived: true,
              },
            });

            // Group by purchase order item ID and sum quantities
            const quantityByPOItem = allGoodsReceiptItems.reduce((acc, item) => {
              if (item.purchaseOrderItemId) {
                acc[item.purchaseOrderItemId] = (acc[item.purchaseOrderItemId] || 0) + item.quantityReceived;
              }
              return acc;
            }, {} as Record<string, number>);

            // Update each purchase order item
            for (const [poItemId, totalQuantityReceived] of Object.entries(quantityByPOItem)) {
              const poItem = await prisma.purchaseOrderItem.findUnique({
                where: { id: poItemId },
              });

              if (poItem) {
                let newStatus = poItem.status;
                if (totalQuantityReceived >= poItem.quantityOrdered) {
                  newStatus = PurchaseOrderItemStatus.FULLY_RECEIVED;
                } else if (totalQuantityReceived > 0) {
                  newStatus = PurchaseOrderItemStatus.PARTIALLY_RECEIVED;
                }

                await prisma.purchaseOrderItem.update({
                  where: { id: poItemId },
                  data: {
                    quantityReceived: totalQuantityReceived,
                    status: newStatus,
                    actualDelivery: new Date(),
                  },
                });
              }
            }

            // Update purchase order status
            const allPOItems = await prisma.purchaseOrderItem.findMany({
              where: { purchaseOrderId: goodsReceipt.purchaseOrderId },
            });

            const allFullyReceived = allPOItems.every(item =>
              item.status === PurchaseOrderItemStatus.FULLY_RECEIVED
            );
            const anyPartiallyReceived = allPOItems.some(item =>
              item.status === PurchaseOrderItemStatus.PARTIALLY_RECEIVED ||
              item.status === PurchaseOrderItemStatus.FULLY_RECEIVED
            );

            const purchaseOrder = await prisma.purchaseOrder.findUnique({
              where: { id: goodsReceipt.purchaseOrderId },
            });

            let newPOStatus = purchaseOrder!.status;
            if (allFullyReceived) {
              newPOStatus = PurchaseOrderStatus.COMPLETED;
            } else if (anyPartiallyReceived) {
              newPOStatus = PurchaseOrderStatus.PARTIALLY_RECEIVED;
            }

            if (newPOStatus !== purchaseOrder!.status) {
              await prisma.purchaseOrder.update({
                where: { id: goodsReceipt.purchaseOrderId },
                data: {
                  status: newPOStatus,
                  actualDelivery: new Date(),
                  updatedBy: userId,
                },
              });
            }
          }
        }

        return prisma.goodsReceipt.findUnique({
          where: { id },
          include: {
            supplier: true,
            purchaseOrder: true,
            items: {
              include: {
                product: true,
                unit: true,
              },
            },
          },
        });
      });

      return updatedReceipt;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Nomor goods receipt sudah digunakan');
        }
      }
      throw error;
    }
  }

  async updateQualityControl(id: string, qualityDto: QualityControlUpdateDto, userId: string) {
    const existingReceipt = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
      existingReceipt.status,
      GoodsReceiptStatus.IN_INSPECTION
    )) {
      throw new BadRequestException(
        `Tidak dapat melakukan inspeksi pada goods receipt dengan status ${existingReceipt.status}`
      );
    }

    const updatedReceipt = await this.prisma.goodsReceipt.update({
      where: { id },
      data: {
        status: GoodsReceiptStatus.IN_INSPECTION,
        qualityStatus: qualityDto.qualityStatus,
        qualityNotes: qualityDto.qualityNotes,
        inspectionDate: qualityDto.inspectionDate ? new Date(qualityDto.inspectionDate) : new Date(),
        inspectionBy: userId,
        updatedBy: userId,
      },
    });

    return this.findOne(updatedReceipt.id);
  }

  async updateStatus(id: string, statusDto: GoodsReceiptStatusUpdateDto, userId: string) {
    const existingReceipt = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
      existingReceipt.status,
      statusDto.status
    )) {
      throw new BadRequestException(
        `Tidak dapat mengubah status dari ${existingReceipt.status} ke ${statusDto.status}`
      );
    }

    const updateData: any = {
      status: statusDto.status,
      updatedBy: userId,
    };

    if (statusDto.notes) {
      updateData.notes = statusDto.notes;
    }

    const updatedReceipt = await this.prisma.goodsReceipt.update({
      where: { id },
      data: updateData,
    });

    return this.findOne(updatedReceipt.id);
  }

  async approve(id: string, userId: string) {
    const existingReceipt = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
      existingReceipt.status,
      GoodsReceiptStatus.APPROVED
    )) {
      throw new BadRequestException(
        `Tidak dapat menyetujui goods receipt dengan status ${existingReceipt.status}`
      );
    }

    // Use transaction to ensure data consistency
    const result = await this.prisma.$transaction(async (prisma) => {
      // Update goods receipt status
      const updatedReceipt = await prisma.goodsReceipt.update({
        where: { id },
        data: {
          status: GoodsReceiptStatus.APPROVED,
          qualityStatus: QualityControlStatus.PASSED,
          updatedBy: userId,
        },
      });

      // Create inventory items for each goods receipt item
      for (const item of existingReceipt.items) {
        if (item.quantityReceived > 0) {
          await this.createInventoryFromGoodsReceiptItem(item, existingReceipt.id, userId, prisma);
        }
      }

      return updatedReceipt;
    });

    return this.findOne(result.id);
  }

  async reject(id: string, reason: string, userId: string) {
    const existingReceipt = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
      existingReceipt.status,
      GoodsReceiptStatus.REJECTED
    )) {
      throw new BadRequestException(
        `Tidak dapat menolak goods receipt dengan status ${existingReceipt.status}`
      );
    }

    const updatedReceipt = await this.prisma.goodsReceipt.update({
      where: { id },
      data: {
        status: GoodsReceiptStatus.REJECTED,
        qualityStatus: QualityControlStatus.FAILED,
        qualityNotes: reason,
        updatedBy: userId,
      },
    });

    return this.findOne(updatedReceipt.id);
  }

  async complete(id: string, userId: string) {
    const existingReceipt = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
      existingReceipt.status,
      GoodsReceiptStatus.COMPLETED
    )) {
      throw new BadRequestException(
        `Tidak dapat menyelesaikan goods receipt dengan status ${existingReceipt.status}`
      );
    }

    const updatedReceipt = await this.prisma.goodsReceipt.update({
      where: { id },
      data: {
        status: GoodsReceiptStatus.COMPLETED,
        updatedBy: userId,
      },
    });

    return this.findOne(updatedReceipt.id);
  }

  async remove(id: string) {
    const existingReceipt = await this.findOne(id);

    // Only allow deletion of pending receipts
    if (existingReceipt.status !== GoodsReceiptStatus.PENDING) {
      throw new BadRequestException('Hanya goods receipt dengan status PENDING yang dapat dihapus');
    }

    await this.prisma.$transaction(async (prisma) => {
      // Delete items first
      await prisma.goodsReceiptItem.deleteMany({
        where: { goodsReceiptId: id },
      });

      // Delete goods receipt
      await prisma.goodsReceipt.delete({
        where: { id },
      });
    });
  }

  async getStats(period?: string) {
    // Calculate date range based on period
    const dateRange = this.calculateDateRange(period);

    const [
      totalReceipts,
      statusCounts,
      qualityStatusCounts,
      totalValue,
      recentReceipts,
      qualityTrends,
      supplierQualityStats
    ] = await Promise.all([
      this.prisma.goodsReceipt.count({
        where: dateRange ? { createdAt: dateRange } : undefined,
      }),
      this.prisma.goodsReceipt.groupBy({
        by: ['status'],
        _count: { status: true },
        where: dateRange ? { createdAt: dateRange } : undefined,
      }),
      this.prisma.goodsReceipt.groupBy({
        by: ['qualityStatus'],
        _count: { qualityStatus: true },
        where: dateRange ? { createdAt: dateRange } : undefined,
      }),
      this.prisma.goodsReceipt.aggregate({
        _sum: { totalAmount: true },
        where: dateRange ? { createdAt: dateRange } : undefined,
      }),
      this.prisma.goodsReceipt.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        where: dateRange ? { createdAt: dateRange } : undefined,
        include: {
          supplier: {
            select: { name: true },
          },
          purchaseOrder: {
            select: { orderNumber: true },
          },
        },
      }),
      this.calculateQualityTrends(dateRange),
      this.calculateSupplierQualityStats(dateRange),
    ]);

    const statusStats = statusCounts.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {} as Record<string, number>);

    const qualityStats = qualityStatusCounts.reduce((acc, item) => {
      acc[item.qualityStatus] = item._count.qualityStatus;
      return acc;
    }, {} as Record<string, number>);

    // Calculate derived statistics that frontend expects
    const pendingInspection = statusStats[GoodsReceiptStatus.PENDING] || 0;
    const approved = statusStats[GoodsReceiptStatus.APPROVED] || 0;
    const rejected = statusStats[GoodsReceiptStatus.REJECTED] || 0;

    // Calculate quality pass rate (approved / total quality checked)
    const totalQualityChecked = approved + rejected;
    const qualityPassRate = totalQualityChecked > 0 ? approved / totalQualityChecked : 0;

    // Calculate average inspection time
    const averageInspectionTime = await this.calculateAverageInspectionTime(dateRange);

    return {
      totalReceipts,
      pendingInspection,
      approved,
      rejected,
      qualityPassRate,
      averageInspectionTime,
      statusStats,
      qualityStats,
      totalValue: Number(totalValue._sum.totalAmount || 0),
      recentReceipts,
      // Enhanced analytics
      qualityTrends,
      supplierQualityStats,
      processingTimeAnalytics: await this.calculateProcessingTimeAnalytics(dateRange),
      volumeTrends: await this.calculateVolumeTrends(dateRange),
      period: period || 'all',
    };
  }

  /**
   * Calculate date range for statistics based on period
   */
  private calculateDateRange(period?: string): { gte: Date } | undefined {
    if (!period || period === 'all') {
      return undefined;
    }

    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        return undefined;
    }

    return { gte: startDate };
  }

  /**
   * Calculate quality trends over time
   */
  private async calculateQualityTrends(dateRange?: { gte: Date }): Promise<any[]> {
    try {
      // Get quality trends for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const trends = await this.prisma.goodsReceipt.groupBy({
        by: ['qualityStatus'],
        _count: { qualityStatus: true },
        where: {
          createdAt: { gte: dateRange?.gte || thirtyDaysAgo },
          qualityStatus: { not: QualityControlStatus.PENDING },
        },
        orderBy: { _count: { qualityStatus: 'desc' } },
      });

      return trends.map(trend => ({
        status: trend.qualityStatus,
        count: trend._count.qualityStatus,
      }));
    } catch (error) {
      console.error('Error calculating quality trends:', error);
      return [];
    }
  }

  /**
   * Calculate supplier quality statistics
   */
  private async calculateSupplierQualityStats(dateRange?: { gte: Date }): Promise<any[]> {
    try {
      const supplierStats = await this.prisma.goodsReceipt.groupBy({
        by: ['supplierId', 'qualityStatus'],
        _count: { qualityStatus: true },
        where: {
          createdAt: dateRange,
          qualityStatus: { not: QualityControlStatus.PENDING },
        },
      });

      // Group by supplier and calculate quality rates
      const supplierQuality = supplierStats.reduce((acc, stat) => {
        if (!acc[stat.supplierId]) {
          acc[stat.supplierId] = { approved: 0, rejected: 0, total: 0 };
        }

        if (stat.qualityStatus === QualityControlStatus.PASSED) {
          acc[stat.supplierId].approved += stat._count.qualityStatus;
        } else if (stat.qualityStatus === QualityControlStatus.FAILED) {
          acc[stat.supplierId].rejected += stat._count.qualityStatus;
        }
        acc[stat.supplierId].total += stat._count.qualityStatus;

        return acc;
      }, {} as Record<string, { approved: number; rejected: number; total: number }>);

      // Get supplier names and calculate quality rates
      const supplierIds = Object.keys(supplierQuality);
      if (supplierIds.length === 0) return [];

      const suppliers = await this.prisma.supplier.findMany({
        where: { id: { in: supplierIds } },
        select: { id: true, name: true },
      });

      return suppliers.map(supplier => {
        const stats = supplierQuality[supplier.id];
        const qualityRate = stats.total > 0 ? stats.approved / stats.total : 0;

        return {
          supplierId: supplier.id,
          supplierName: supplier.name,
          totalReceipts: stats.total,
          approvedReceipts: stats.approved,
          rejectedReceipts: stats.rejected,
          qualityRate,
        };
      }).sort((a, b) => b.qualityRate - a.qualityRate);
    } catch (error) {
      console.error('Error calculating supplier quality stats:', error);
      return [];
    }
  }

  /**
   * Calculate average inspection time in hours
   * Returns the average time between receipt creation and inspection completion
   */
  private async calculateAverageInspectionTime(dateRange?: { gte: Date }): Promise<number> {
    try {
      // Get all goods receipts that have been inspected (have inspectionDate)
      const inspectedReceipts = await this.prisma.goodsReceipt.findMany({
        where: {
          inspectionDate: { not: null },
          ...(dateRange && { createdAt: dateRange }),
        },
        select: {
          createdAt: true,
          inspectionDate: true,
        },
      });

      if (inspectedReceipts.length === 0) {
        return 0;
      }

      // Calculate inspection time for each receipt in hours
      const inspectionTimes = inspectedReceipts
        .filter(receipt => receipt.inspectionDate !== null)
        .map(receipt => {
          const createdAt = new Date(receipt.createdAt);
          const inspectionDate = new Date(receipt.inspectionDate!);
          const timeDiffMs = inspectionDate.getTime() - createdAt.getTime();
          return timeDiffMs / (1000 * 60 * 60); // Convert to hours
        });

      if (inspectionTimes.length === 0) {
        return 0;
      }

      // Calculate average inspection time
      const totalTime = inspectionTimes.reduce((sum, time) => sum + time, 0);
      const averageTime = totalTime / inspectionTimes.length;

      // Return average time, ensuring it's not negative
      return Math.max(0, averageTime);
    } catch (error) {
      // Log error and return 0 as fallback
      console.error('Error calculating average inspection time:', error);
      return 0;
    }
  }

  /**
   * Calculate processing time analytics
   */
  private async calculateProcessingTimeAnalytics(dateRange?: { gte: Date }): Promise<any> {
    try {
      const receipts = await this.prisma.goodsReceipt.findMany({
        where: {
          createdAt: dateRange,
          status: { not: GoodsReceiptStatus.PENDING },
        },
        select: {
          createdAt: true,
          updatedAt: true,
          status: true,
          inspectionDate: true,
        },
      });

      if (receipts.length === 0) {
        return {
          averageProcessingTime: 0,
          averageInspectionTime: 0,
          statusTransitionTimes: {},
          efficiencyMetrics: {
            fastProcessing: 0,
            slowProcessing: 0,
            averageProcessing: 0,
          },
        };
      }

      // Calculate processing times
      const processingTimes = receipts.map(receipt => {
        const created = new Date(receipt.createdAt);
        const updated = new Date(receipt.updatedAt);
        return (updated.getTime() - created.getTime()) / (1000 * 60 * 60); // hours
      });

      // Calculate inspection times for receipts with inspection dates
      const inspectionTimes = receipts
        .filter(r => r.inspectionDate)
        .map(receipt => {
          const created = new Date(receipt.createdAt);
          const inspected = new Date(receipt.inspectionDate!);
          return (inspected.getTime() - created.getTime()) / (1000 * 60 * 60); // hours
        });

      const avgProcessingTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
      const avgInspectionTime = inspectionTimes.length > 0
        ? inspectionTimes.reduce((a, b) => a + b, 0) / inspectionTimes.length
        : 0;

      // Calculate efficiency metrics
      const fastThreshold = 24; // 24 hours
      const slowThreshold = 72; // 72 hours

      const fastProcessing = processingTimes.filter(t => t <= fastThreshold).length;
      const slowProcessing = processingTimes.filter(t => t >= slowThreshold).length;
      const averageProcessing = processingTimes.filter(t => t > fastThreshold && t < slowThreshold).length;

      return {
        averageProcessingTime: avgProcessingTime,
        averageInspectionTime: avgInspectionTime,
        totalReceipts: receipts.length,
        efficiencyMetrics: {
          fastProcessing,
          slowProcessing,
          averageProcessing,
          fastPercentage: (fastProcessing / receipts.length) * 100,
          slowPercentage: (slowProcessing / receipts.length) * 100,
        },
      };
    } catch (error) {
      console.error('Error calculating processing time analytics:', error);
      return {
        averageProcessingTime: 0,
        averageInspectionTime: 0,
        efficiencyMetrics: { fastProcessing: 0, slowProcessing: 0, averageProcessing: 0 },
      };
    }
  }

  /**
   * Calculate volume trends over time
   */
  private async calculateVolumeTrends(dateRange?: { gte: Date }): Promise<any[]> {
    try {
      const startDate = dateRange?.gte || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const endDate = new Date();

      // Group receipts by day
      const dailyVolumes = await this.prisma.goodsReceipt.groupBy({
        by: ['createdAt'],
        _count: { id: true },
        _sum: { totalAmount: true },
        where: {
          createdAt: { gte: startDate, lte: endDate },
        },
        orderBy: { createdAt: 'asc' },
      });

      // Process data to create daily trends
      const trends: Array<{ date: string; count: number; value: number }> = [];
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const dayData = dailyVolumes.filter(d =>
          d.createdAt.toISOString().split('T')[0] === dateStr
        );

        const dayCount = dayData.reduce((sum, d) => sum + d._count.id, 0);
        const dayValue = dayData.reduce((sum, d) => sum + Number(d._sum.totalAmount || 0), 0);

        trends.push({
          date: dateStr,
          count: dayCount,
          value: dayValue,
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return trends;
    } catch (error) {
      console.error('Error calculating volume trends:', error);
      return [];
    }
  }

  /**
   * Create inventory item from goods receipt item
   * Handles unit conversion and creates appropriate stock movement records
   */
  private async createInventoryFromGoodsReceiptItem(
    goodsReceiptItem: any,
    goodsReceiptId: string,
    userId: string,
    prisma: any
  ) {
    try {
      // Get product with base unit information
      const product = await prisma.product.findUnique({
        where: { id: goodsReceiptItem.productId },
        include: { baseUnit: true },
      });

      if (!product) {
        throw new BadRequestException(`Produk dengan ID ${goodsReceiptItem.productId} tidak ditemukan`);
      }

      // Convert quantity to base units
      const conversionResult = await this.unitConversionService.convertToBaseUnit(
        goodsReceiptItem.productId,
        goodsReceiptItem.unitId,
        goodsReceiptItem.quantityReceived
      );

      if (!conversionResult.success) {
        throw new BadRequestException(
          `Gagal mengkonversi unit untuk produk ${product.name}: ${conversionResult.error}`
        );
      }

      const baseQuantity = conversionResult.convertedQuantity!;

      // Calculate cost price per base unit
      const totalCostInReceiptUnit = Number(goodsReceiptItem.unitPrice) * goodsReceiptItem.quantityReceived;
      const costPricePerBaseUnit = totalCostInReceiptUnit / baseQuantity;

      // Get goods receipt information for reference
      const goodsReceipt = await prisma.goodsReceipt.findUnique({
        where: { id: goodsReceiptId },
        select: { id: true, receiptNumber: true, supplierId: true },
      });

      // Create inventory item directly (without using InventoryService to control stock movement)
      const inventoryItem = await prisma.inventoryItem.create({
        data: {
          productId: goodsReceiptItem.productId,
          unitId: product.baseUnitId, // Always store in base units
          batchNumber: goodsReceiptItem.batchNumber,
          expiryDate: goodsReceiptItem.expiryDate ? new Date(goodsReceiptItem.expiryDate) : null,
          quantityOnHand: Math.floor(baseQuantity), // Ensure integer quantity
          costPrice: new Prisma.Decimal(costPricePerBaseUnit),
          sellingPrice: null, // Will be set separately if needed
          location: goodsReceiptItem.storageLocation || 'Default',
          receivedDate: new Date(),
          supplierId: goodsReceipt?.supplierId,
          isActive: true,
          notes: `Dibuat dari goods receipt ${goodsReceipt?.receiptNumber || 'N/A'}`,
          createdBy: userId,
        },
      });

      // Create stock movement with GOODS_RECEIPT reference type
      if (inventoryItem.quantityOnHand > 0) {
        await prisma.stockMovement.create({
          data: {
            inventoryItemId: inventoryItem.id,
            type: StockMovementType.IN,
            quantity: inventoryItem.quantityOnHand,
            unitPrice: inventoryItem.costPrice,
            referenceType: ReferenceType.GOODS_RECEIPT,
            referenceId: goodsReceipt?.id,
            referenceNumber: goodsReceipt?.receiptNumber,
            reason: 'Penerimaan barang dari goods receipt',
            notes: `Item diterima dari goods receipt ${goodsReceipt?.receiptNumber || 'N/A'}`,
            createdBy: userId,
          },
        });
      }

      return inventoryItem;
    } catch (error) {
      throw new InternalServerErrorException(
        `Gagal membuat item inventori dari goods receipt: ${error.message}`
      );
    }
  }
}
