import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { PurchaseOrderService } from './services/purchase-order.service';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from './dto/update-purchase-order.dto';
import { PurchaseOrderQueryDto, PurchaseOrderApprovalDto, PurchaseOrderStatusUpdateDto } from './dto/purchase-order-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';

@Controller('purchase-orders')
@UseGuards(JwtAuthGuard)
export class PurchaseOrderController {
  constructor(private readonly purchaseOrderService: PurchaseOrderService) { }

  @Post()
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create
  async create(@Body() createPurchaseOrderDto: CreatePurchaseOrderDto, @Request() req) {
    return this.purchaseOrderService.create(createPurchaseOrderDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: PurchaseOrderQueryDto) {
    return this.purchaseOrderService.findAll(query);
  }

  @Get('stats')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view stats
  async getStats(@Query('period') period?: string) {
    return this.purchaseOrderService.getStats(period);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.purchaseOrderService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update
  async update(
    @Param('id') id: string,
    @Body() updatePurchaseOrderDto: UpdatePurchaseOrderDto,
    @Request() req,
  ) {
    return this.purchaseOrderService.update(id, updatePurchaseOrderDto, req.user.id);
  }

  @Post(':id/approve')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can approve
  async approve(
    @Param('id') id: string,
    @Body() approvalDto: PurchaseOrderApprovalDto,
    @Request() req,
  ) {
    return this.purchaseOrderService.approve(id, approvalDto, req.user.id);
  }

  @Patch(':id/status')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update status
  async updateStatus(
    @Param('id') id: string,
    @Body() statusDto: PurchaseOrderStatusUpdateDto,
    @Request() req,
  ) {
    return this.purchaseOrderService.updateStatus(id, statusDto, req.user.id);
  }

  @Post(':id/cancel')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can cancel
  async cancel(
    @Param('id') id: string,
    @Body('reason') reason: string,
    @Request() req,
  ) {
    if (!reason) {
      throw new BadRequestException('Alasan pembatalan wajib diisi');
    }
    return this.purchaseOrderService.cancel(id, reason, req.user.id);
  }

  @Delete(':id')
  @UseGuards(AdminGuard) // Only Admin can hard delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.purchaseOrderService.remove(id);
  }
}
