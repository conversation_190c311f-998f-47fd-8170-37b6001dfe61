import { IsOptional, IsEnum } from 'class-validator';

/**
 * Query DTO for goods receipt statistics endpoint
 */
export class GoodsReceiptStatsQueryDto {
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', 'quarter', 'year', 'all'])
  period?: '7d' | '30d' | '90d' | 'quarter' | 'year' | 'all';
}

/**
 * Response DTO for goods receipt statistics
 * This ensures proper validation and documentation of the API response
 */
export class GoodsReceiptStatsResponseDto {
  totalReceipts: number;
  pendingInspection: number;
  approved: number;
  rejected: number;
  qualityPassRate: number;
  averageInspectionTime: number;
  totalValue: number;
  statusStats: Record<string, number>;
  qualityStats: Record<string, number>;
  recentReceipts: any[];
  qualityTrends?: any[];
  supplierQualityStats?: any[];
  processingTimeAnalytics?: any;
  volumeTrends?: any[];
  period: string;
}
