import { IsOptional, IsString, IsEnum, IsInt, Min, IsDateString, IsBoolean, MinLength } from 'class-validator';
import { Transform } from 'class-transformer';
import { GoodsReceiptStatus, QualityControlStatus } from '@prisma/client';

export class GoodsReceiptQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  supplierId?: string;

  @IsOptional()
  @IsString()
  purchaseOrderId?: string;

  @IsOptional()
  @IsEnum(GoodsReceiptStatus)
  status?: GoodsReceiptStatus;

  @IsOptional()
  @IsEnum(QualityControlStatus)
  qualityStatus?: QualityControlStatus;

  @IsOptional()
  @IsDateString()
  receiptDateFrom?: string;

  @IsOptional()
  @IsDateString()
  receiptDateTo?: string;

  @IsOptional()
  @IsDateString()
  deliveryDateFrom?: string;

  @IsOptional()
  @IsDateString()
  deliveryDateTo?: string;

  @IsOptional()
  @IsString()
  inspectionBy?: string;

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class QualityControlUpdateDto {
  @IsEnum(QualityControlStatus)
  qualityStatus: QualityControlStatus;

  @IsOptional()
  @IsString()
  qualityNotes?: string;

  @IsOptional()
  @IsDateString()
  inspectionDate?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null) return undefined;
    return value === 'true' || value === true;
  })
  @IsBoolean()
  temperatureCheck?: boolean;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null) return undefined;
    return value === 'true' || value === true;
  })
  @IsBoolean()
  packagingCheck?: boolean;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null) return undefined;
    return value === 'true' || value === true;
  })
  @IsBoolean()
  documentationCheck?: boolean;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null) return undefined;
    return value === 'true' || value === true;
  })
  @IsBoolean()
  bpomCheck?: boolean;
}

export class GoodsReceiptStatusUpdateDto {
  @IsEnum(GoodsReceiptStatus)
  status: GoodsReceiptStatus;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class RejectGoodsReceiptDto {
  @IsString()
  @MinLength(5, { message: 'Alasan penolakan minimal 5 karakter' })
  reason: string;
}
