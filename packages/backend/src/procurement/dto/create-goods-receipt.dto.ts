import { 
  IsString, 
  IsOptional, 
  IsEnum, 
  IsArray, 
  ValidateNested, 
  IsNumber, 
  IsInt, 
  Min, 
  IsDateString, 
  ArrayMinSize,
  IsPositive
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { GoodsReceiptStatus, QualityControlStatus } from '@prisma/client';

export class CreateGoodsReceiptItemDto {
  @IsOptional()
  @IsString()
  purchaseOrderItemId?: string; // Optional - can receive items not in PO

  @IsString()
  productId: string;

  @IsString()
  unitId: string;

  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  @IsInt()
  @Min(0)
  quantityOrdered?: number; // Quantity originally ordered (if from PO)

  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  quantityReceived: number;

  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  unitPrice: number;

  // Batch and Expiry Information
  @IsOptional()
  @IsString()
  batchNumber?: string;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @IsOptional()
  @IsDateString()
  manufacturingDate?: string;

  // Quality Control Information
  @IsOptional()
  @IsEnum(QualityControlStatus)
  qualityStatus?: QualityControlStatus;

  @IsOptional()
  @IsString()
  qualityNotes?: string;

  @IsOptional()
  @IsDateString()
  inspectionDate?: string;

  @IsOptional()
  @IsString()
  inspectionBy?: string;

  // Storage Information
  @IsOptional()
  @IsString()
  storageLocation?: string;

  @IsOptional()
  @IsString()
  storageCondition?: string;

  // Condition Information
  @IsOptional()
  @IsString()
  conditionOnReceipt?: string;

  @IsOptional()
  @IsString()
  damageNotes?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CreateGoodsReceiptDto {
  @IsOptional()
  @IsString()
  receiptNumber?: string; // Will be auto-generated if not provided

  @IsOptional()
  @IsString()
  purchaseOrderId?: string; // Optional - can receive goods without PO

  @IsString()
  supplierId: string;

  @IsOptional()
  @IsEnum(GoodsReceiptStatus)
  status?: GoodsReceiptStatus;

  @IsOptional()
  @IsDateString()
  receiptDate?: string;

  @IsOptional()
  @IsDateString()
  deliveryDate?: string;

  @IsOptional()
  @IsString()
  invoiceNumber?: string;

  @IsOptional()
  @IsString()
  deliveryNote?: string;

  // Quality Control Information
  @IsOptional()
  @IsDateString()
  inspectionDate?: string;

  @IsOptional()
  @IsString()
  inspectionBy?: string;

  @IsOptional()
  @IsEnum(QualityControlStatus)
  qualityStatus?: QualityControlStatus;

  @IsOptional()
  @IsString()
  qualityNotes?: string;

  // Delivery Information
  @IsOptional()
  @IsString()
  deliveredBy?: string;

  @IsOptional()
  @IsString()
  receivedBy?: string;

  @IsOptional()
  @IsString()
  deliveryCondition?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsString()
  internalNotes?: string;

  // Goods Receipt Items
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateGoodsReceiptItemDto)
  items: CreateGoodsReceiptItemDto[];
}
