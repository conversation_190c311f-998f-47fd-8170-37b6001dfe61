import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import {
  StockAllocationDto,
  AllocationMethod,
  AllocationResult,
  AllocationBatch,
  AllocationOptions
} from '../dto/stock-allocation.dto';
import { PrismaClient, StockMovementType } from '@prisma/client';
import { ReferenceType } from '../dto/reference-types.enum';
import { Decimal, DefaultArgs } from '@prisma/client/runtime/library';
import type { Prisma } from '@prisma/client';

@Injectable()
export class StockAllocationService {
  constructor(private prisma: PrismaService) { }

  async allocateStock(
    allocationDto: StockAllocationDto,
    userId: string,
    options?: AllocationOptions,
    tx?: Omit<PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">
  ): Promise<AllocationResult> {
    const {
      productId,
      requestedQuantity,
      method,
      previewOnly = false,
      allowPartialAllocation = true,
      nearExpiryWarningDays = 30,
      reason,
      notes,
      referenceType,
      referenceId,
      referenceNumber
    } = allocationDto;

    // Validate product exists
    const product = await (tx || this.prisma).product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, isActive: true }
    });

    if (!product) {
      return this.createEmptyAllocationResult(
        productId,
        requestedQuantity,
        method,
        previewOnly,
        [`Produk dengan ID ${productId} tidak ditemukan`]
      );
    }

    if (!product.isActive) {
      return this.createEmptyAllocationResult(
        productId,
        requestedQuantity,
        method,
        previewOnly,
        [`Produk ${product.name} tidak aktif`]
      );
    }

    // Get available inventory items
    const availableItems = await this.getAvailableInventoryItems(productId, method, tx);

    if (!availableItems || availableItems.length === 0) {
      return this.createEmptyAllocationResult(
        productId,
        requestedQuantity,
        method,
        previewOnly,
        ['Tidak ada stok tersedia untuk produk ini']
      );
    }

    // Perform allocation
    const allocationResult = await this.performAllocation(
      availableItems,
      requestedQuantity,
      method,
      nearExpiryWarningDays,
      allowPartialAllocation,
      tx
    );

    // If this is just a preview, don't actually allocate anything
    if (!previewOnly) {
      await this.executeAllocation(
        allocationResult.batches,
        productId,
        userId,
        reason || `Alokasi stok menggunakan metode ${method}`,
        notes,
        method, // Pass the allocation method to ensure it's stored correctly
        referenceType as ReferenceType || ReferenceType.ALLOCATION,
        referenceId,
        referenceNumber,
        tx
      );
    }

    return {
      success: allocationResult.success,
      productId,
      requestedQuantity,
      allocatedQuantity: allocationResult.allocatedQuantity,
      method,
      batches: allocationResult.batches,
      shortfall: allocationResult.shortfall,
      warnings: allocationResult.warnings,
      errors: allocationResult.errors,
      totalCost: allocationResult.totalCost,
      averageCostPrice: allocationResult.averageCostPrice,
      previewOnly
    };
  }

  private async getAvailableInventoryItems(productId: string, method: AllocationMethod, tx?: Omit<PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">) {
    const now = new Date();

    // Get inventory items with relations
    const items = await (tx || this.prisma).inventoryItem.findMany({
      where: {
        productId,
        isActive: true,
        quantityOnHand: { gt: 0 },
        // Only consider non-expired items
        OR: [
          { expiryDate: null },
          { expiryDate: { gt: now } }
        ]
      },
      orderBy: method === AllocationMethod.FEFO
        ? [
          { expiryDate: 'asc' },
          { receivedDate: 'asc' },
          { createdAt: 'asc' }
        ]
        : [
          { receivedDate: 'asc' },
          { createdAt: 'asc' }
        ],
      include: {
        product: { include: { baseUnit: true } },
        unit: true,
        supplier: true
      }
    });

    // Filter items that have available quantity (not fully allocated)
    const availableItems = items.filter(item =>
      (item.quantityOnHand - item.quantityAllocated) > 0
    );

    // Double check sorting for FEFO to ensure items with null expiry dates are handled correctly
    if (method === AllocationMethod.FEFO) {
      return availableItems.sort((a, b) => {
        // Handle null expiry dates - null dates should come after non-null dates
        if (a.expiryDate === null && b.expiryDate === null) {
          // If both have null expiry dates, sort by received date
          return a.receivedDate.getTime() - b.receivedDate.getTime();
        }
        if (a.expiryDate === null) return 1; // a has null expiry, b comes first
        if (b.expiryDate === null) return -1; // b has null expiry, a comes first

        // Both have expiry dates, sort by expiry date
        const expiryComparison = a.expiryDate.getTime() - b.expiryDate.getTime();
        if (expiryComparison !== 0) return expiryComparison;

        // If expiry dates are the same, sort by received date
        const receivedComparison = a.receivedDate.getTime() - b.receivedDate.getTime();
        if (receivedComparison !== 0) return receivedComparison;

        // Last resort: sort by creation date
        return a.createdAt.getTime() - b.createdAt.getTime();
      });
    }

    return availableItems;
  }

  private async performAllocation(
    availableItems: any[],
    requestedQuantity: number,
    method: AllocationMethod,
    nearExpiryWarningDays: number,
    allowPartialAllocation: boolean,
    tx?: Omit<PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">
  ): Promise<Omit<AllocationResult, 'productId' | 'requestedQuantity' | 'method' | 'previewOnly'>> {
    const batches: AllocationBatch[] = [];
    const warnings: string[] = [];
    const errors: string[] = [];
    let remainingQuantity = requestedQuantity;
    let totalCost = 0;
    let totalAllocatedQuantity = 0;

    const now = new Date();
    const nearExpiryThreshold = new Date();
    nearExpiryThreshold.setDate(now.getDate() + nearExpiryWarningDays);

    for (const item of availableItems) {
      if (remainingQuantity <= 0) break;

      // Calculate available quantity (what's not already allocated)
      const availableQuantity = item.quantityOnHand - item.quantityAllocated;
      if (availableQuantity <= 0) continue; // Skip if no available quantity

      const allocatedFromThisBatch = Math.min(remainingQuantity, availableQuantity);

      // Check for near expiry warning
      let isNearExpiry = false;
      let daysUntilExpiry: number | undefined;

      if (item.expiryDate) {
        const timeDiff = item.expiryDate.getTime() - now.getTime();
        daysUntilExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));

        if (daysUntilExpiry <= nearExpiryWarningDays && daysUntilExpiry > 0) {
          isNearExpiry = true;
          warnings.push(
            `Batch ${item.batchNumber || 'N/A'} kedaluwarsa dalam ${daysUntilExpiry} hari (${item.expiryDate.toLocaleDateString('id-ID')})`
          );
        }
      }

      const batch: AllocationBatch = {
        inventoryItemId: item.id,
        batchNumber: item.batchNumber,
        expiryDate: item.expiryDate,
        receivedDate: item.receivedDate,
        availableQuantity,
        allocatedQuantity: allocatedFromThisBatch,
        costPrice: Number(item.costPrice),
        location: item.location,
        isNearExpiry,
        daysUntilExpiry
      };

      batches.push(batch);
      totalCost += allocatedFromThisBatch * Number(item.costPrice);
      totalAllocatedQuantity += allocatedFromThisBatch;
      remainingQuantity -= allocatedFromThisBatch;
    }

    // Check if allocation was successful
    let success = remainingQuantity === 0;
    let finalAllocatedQuantity = totalAllocatedQuantity;
    let finalBatches = batches;
    let finalTotalCost = totalCost;

    if (remainingQuantity > 0) {
      if (allowPartialAllocation) {
        warnings.push(`Alokasi sebagian: ${remainingQuantity} unit tidak dapat dialokasikan karena stok tidak mencukupi`);
        success = totalAllocatedQuantity > 0;
      } else {
        errors.push(`Stok tidak mencukupi: kekurangan ${remainingQuantity} unit dari jumlah yang diminta`);
        success = false;
        // When partial allocation is not allowed and we can't fulfill the full request,
        // don't allocate anything
        finalAllocatedQuantity = 0;
        finalBatches = [];
        finalTotalCost = 0;
      }
    }

    const averageCostPrice = finalAllocatedQuantity > 0 ? finalTotalCost / finalAllocatedQuantity : 0;

    return {
      success,
      allocatedQuantity: finalAllocatedQuantity,
      batches: finalBatches,
      shortfall: remainingQuantity > 0 ? remainingQuantity : 0,
      warnings,
      errors,
      totalCost: finalTotalCost,
      averageCostPrice
    };
  }

  private async executeAllocation(
    batches: AllocationBatch[],
    productId: string,
    userId: string,
    reason: string,
    notes?: string,
    method: AllocationMethod = AllocationMethod.FIFO,
    referenceType?: ReferenceType,
    referenceId?: string,
    referenceNumber?: string,
    tx?: Omit<PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">
  ): Promise<void> {
    // IMPORTANT: Stock allocation process
    // 1. We only increment the quantityAllocated to track allocated stock
    // 2. We do NOT decrement the quantityOnHand as that represents physical stock
    // 3. This approach ensures that:
    //    - Physical stock (quantityOnHand) remains unchanged during allocation
    //    - The system tracks which stock has been allocated via quantityAllocated
    //    - Available stock is calculated as (quantityOnHand - quantityAllocated)
    if (tx) {
      await this.processAllocation(tx, batches, productId, userId, reason, method, referenceId, referenceNumber, referenceType, notes);
    } else {
      await this.prisma.$transaction(async (tx) => {
        await this.processAllocation(tx, batches, productId, userId, reason, method, referenceId, referenceNumber, referenceType, notes);
      });
    }
  }

  private async processAllocation(tx: Omit<PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">, batches: AllocationBatch[], productId: string, userId: string, reason: string, method: AllocationMethod, referenceId?: string, referenceNumber?: string, referenceType?: ReferenceType, notes?: string) {
    for (const batch of batches) {
      // Update inventory allocation (only increment quantityAllocated)
      await tx.inventoryItem.update({
        where: { id: batch.inventoryItemId },
        data: {
          quantityAllocated: { increment: batch.allocatedQuantity }, // Track allocation
          updatedBy: userId
        }
      });

      // Create stock movement record with allocation method explicitly included in notes
      // This ensures the allocation method is properly stored and can be retrieved later
      const allocationNotes = notes || `Metode ${method}: Dialokasikan ${batch.allocatedQuantity} unit dari batch ${batch.batchNumber || 'N/A'}`;

      await tx.stockMovement.create({
        data: {
          inventoryItemId: batch.inventoryItemId,
          type: StockMovementType.ALLOCATION,
          quantity: batch.allocatedQuantity,
          unitPrice: new Decimal(batch.costPrice),
          referenceType: referenceType as ReferenceType || ReferenceType.ALLOCATION,
          referenceId: referenceId,
          referenceNumber: referenceNumber,
          reason,
          notes: allocationNotes,
          createdBy: userId
        }
      });
    }
  }

  private createEmptyAllocationResult(
    productId: string,
    requestedQuantity: number,
    method: AllocationMethod,
    previewOnly: boolean,
    errors: string[]
  ): AllocationResult {
    return {
      success: false,
      productId,
      requestedQuantity,
      allocatedQuantity: 0,
      method,
      batches: [],
      shortfall: requestedQuantity,
      warnings: [],
      errors,
      totalCost: 0,
      averageCostPrice: 0,
      previewOnly
    };
  }

  async getAvailableStockSummary(productId: string): Promise<{
    totalAvailable: number;
    batchCount: number;
    nearExpiryCount: number;
    nearExpiryQuantity: number;
    expiredCount: number;
    expiredQuantity: number;
    oldestBatch?: Date;
    newestBatch?: Date;
    earliestExpiry?: Date;
    unit?: {
      name: string;
      abbreviation: string;
    };
  }> {
    const now = new Date();
    const thirtyDaysFromNow = new Date(now);
    thirtyDaysFromNow.setDate(now.getDate() + 30);

    // Calculate available stock as (quantityOnHand - quantityAllocated)
    const baseWhere = {
      productId,
      isActive: true,
      quantityOnHand: { gt: 0 }
    };

    // Use raw SQL for accurate calculation of available stock
    const availableStockQuery = `
      SELECT 
        COUNT(id) as batch_count,
        SUM(("quantityOnHand" - "quantityAllocated")) as total_available
      FROM "inventory_items"
      WHERE "productId" = $1
        AND "isActive" = true
        AND ("quantityOnHand" - "quantityAllocated") > 0
    `;

    const nearExpiryQuery = `
      SELECT 
        COUNT(id) as batch_count,
        SUM(("quantityOnHand" - "quantityAllocated")) as available_quantity
      FROM "inventory_items"
      WHERE "productId" = $1
        AND "isActive" = true
        AND ("quantityOnHand" - "quantityAllocated") > 0
        AND "expiryDate" IS NOT NULL
        AND "expiryDate" > $2
        AND "expiryDate" <= $3
    `;

    const expiredQuery = `
      SELECT 
        COUNT(id) as batch_count,
        SUM(("quantityOnHand" - "quantityAllocated")) as available_quantity
      FROM "inventory_items"
      WHERE "productId" = $1
        AND "isActive" = true
        AND ("quantityOnHand" - "quantityAllocated") > 0
        AND "expiryDate" IS NOT NULL
        AND "expiryDate" <= $2
    `;

    const [
      totalStatsResult,
      nearExpiryStatsResult,
      expiredStatsResult,
      oldestBatch,
      newestBatch,
      earliestExpiry
    ] = await Promise.all([
      this.prisma.$queryRawUnsafe(availableStockQuery, productId) as Promise<Array<{ batch_count: number, total_available: string }>>,
      this.prisma.$queryRawUnsafe(nearExpiryQuery, productId, now, thirtyDaysFromNow) as Promise<Array<{ batch_count: number, available_quantity: string }>>,
      this.prisma.$queryRawUnsafe(expiredQuery, productId, now) as Promise<Array<{ batch_count: number, available_quantity: string }>>,
      this.prisma.inventoryItem.findFirst({
        where: {
          ...baseWhere,
          quantityOnHand: { gt: 0 }
        },
        orderBy: { receivedDate: 'asc' },
        select: { receivedDate: true }
      }),
      this.prisma.inventoryItem.findFirst({
        where: {
          ...baseWhere,
          quantityOnHand: { gt: 0 }
        },
        orderBy: { receivedDate: 'desc' },
        select: { receivedDate: true }
      }),
      this.prisma.inventoryItem.findFirst({
        where: {
          ...baseWhere,
          expiryDate: { not: null, gt: now },
          quantityOnHand: { gt: 0 }
        },
        orderBy: { expiryDate: 'asc' },
        select: { expiryDate: true }
      })
    ]);

    // Parse the results from raw queries
    const totalStats = totalStatsResult[0] || { batch_count: 0, total_available: '0' };
    const nearExpiryStats = nearExpiryStatsResult[0] || { batch_count: 0, available_quantity: '0' };
    const expiredStats = expiredStatsResult[0] || { batch_count: 0, available_quantity: '0' };

    // Convert string values to numbers with proper null handling
    const totalAvailable = totalStats.total_available === null || totalStats.total_available === undefined
      ? 0
      : (parseInt(totalStats.total_available) || 0);
    const nearExpiryQuantity = nearExpiryStats.available_quantity === null || nearExpiryStats.available_quantity === undefined
      ? 0
      : (parseInt(nearExpiryStats.available_quantity) || 0);
    const expiredQuantity = expiredStats.available_quantity === null || expiredStats.available_quantity === undefined
      ? 0
      : (parseInt(expiredStats.available_quantity) || 0);

    // Get product unit information
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      include: { baseUnit: true }
    });

    return {
      totalAvailable,
      batchCount: totalStats.batch_count || 0,
      nearExpiryCount: nearExpiryStats.batch_count || 0,
      nearExpiryQuantity,
      expiredCount: expiredStats.batch_count || 0,
      expiredQuantity,
      oldestBatch: oldestBatch?.receivedDate || undefined,
      newestBatch: newestBatch?.receivedDate || undefined,
      earliestExpiry: earliestExpiry?.expiryDate || undefined,
      unit: product?.baseUnit ? {
        name: product.baseUnit.name,
        abbreviation: product.baseUnit.abbreviation
      } : undefined
    };
  }
}
