import { Module } from '@nestjs/common';
import { UnitConversionService } from './services/unit-conversion.service';
import { FileUploadService } from './services/file-upload.service';
import { ReferenceGeneratorService } from './services/reference-generator.service';
import { PrismaModule } from '../prisma/prisma.module';
import { SettingsModule } from '../settings/settings.module';

@Module({
  imports: [PrismaModule, SettingsModule],
  providers: [
    UnitConversionService,
    FileUploadService,
    ReferenceGeneratorService,
  ],
  exports: [
    UnitConversionService,
    FileUploadService,
    ReferenceGeneratorService,
  ],
})
export class CommonModule { }
