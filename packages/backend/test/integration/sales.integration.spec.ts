import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';
import { StockMovementType, SaleStatus, PaymentMethod, ReferenceType } from '@prisma/client';

describe('Sales Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testCustomerId: string;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;

  let testSaleId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test data needed for sales
    const supplier = await testSetup.createTestSupplier(ctx.users.admin.id);
    testSupplierId = supplier.id;

    const baseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
    const product = await testSetup.createTestProduct(baseUnit.id, ctx.users.admin.id);
    testProductId = product.id;
    testUnitId = baseUnit.id;

    await testSetup.createTestInventoryItem(testProductId, testUnitId, testSupplierId, ctx.users.admin.id);

    const customer = await testSetup.createTestCustomer(ctx.users.admin.id);
    testCustomerId = customer.id;
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/sales', () => {
    it('should create sale with registered customer', async () => {
      const saleData = {
        customerId: testCustomerId,
        paymentMethod: 'CASH',
        amountPaid: 20000,
        taxAmount: 1500,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.customerId).toBe(testCustomerId);
      expect(response.body.cashierId).toBe(ctx.users.cashier.id);
      expect(response.body.status).toBe('COMPLETED');
      expect(response.body.paymentMethod).toBe('CASH');
      expect(response.body.subtotal).toBe(15000); // 2 * 7500
      expect(response.body.taxAmount).toBe(1500);
      expect(response.body.totalAmount).toBe(16500); // 15000 + 1500
      expect(response.body.amountPaid).toBe(20000);
      expect(response.body.changeAmount).toBe(3500); // 20000 - 16500
      expect(response.body.saleNumber).toBeDefined();
      expect(response.body.saleNumber).toMatch(/^TRX-\d{8}-\d{3}$/);

      testSaleId = response.body.id;
    });

    it('should create sale with walk-in customer', async () => {
      const saleData = {
        customerName: 'Walk-in Customer',
        customerPhone: '+62 813 1234 5678',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.customerId).toBeNull();
      expect(response.body.customerName).toBe('Walk-in Customer');
      expect(response.body.customerPhone).toBe('+62 813 1234 5678');
      expect(response.body.subtotal).toBe(7500);
      expect(response.body.totalAmount).toBe(7500);
      expect(response.body.changeAmount).toBe(2500);
    });

    it('should create sale as admin', async () => {
      const saleData = {
        customerName: 'Admin Sale Customer',
        paymentMethod: 'TRANSFER',
        amountPaid: 15000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.cashierId).toBe(ctx.users.admin.id);
      expect(response.body.paymentMethod).toBe('TRANSFER');
    });

    it('should create sale as pharmacist', async () => {
      const saleData = {
        customerName: 'Pharmacist Sale Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.cashierId).toBe(ctx.users.pharmacist.id);
    });

    it('should create sale with multiple items', async () => {
      // Create another product for multi-item test
      const baseUnit2 = await testSetup.createTestProductUnit(ctx.users.admin.id);
      const product2 = await testSetup.createTestProduct(baseUnit2.id, ctx.users.admin.id);
      await testSetup.createTestInventoryItem(product2.id, baseUnit2.id, testSupplierId, ctx.users.admin.id);

      const saleData = {
        customerName: 'Multi-item Customer',
        paymentMethod: 'CASH',
        amountPaid: 25000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
          {
            productId: product2.id,
            unitId: baseUnit2.id,
            quantity: 2,
            unitPrice: 5000,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(17500); // 7500 + (2 * 5000)
      expect(response.body.totalAmount).toBe(17500);
      expect(response.body.changeAmount).toBe(7500); // 25000 - 17500
    });

    it('should create sale with item-level discounts', async () => {
      const saleData = {
        customerName: 'Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 15000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'PERCENTAGE',
            discountValue: 10, // 10% discount
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(13500); // 15000 - (15000 * 0.1)
      expect(response.body.totalAmount).toBe(13500);
      expect(response.body.changeAmount).toBe(1500);
    });

    it('should create sale with sale-level discount', async () => {
      const saleData = {
        customerName: 'Sale Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 15000,
        discountType: 'FIXED_AMOUNT',
        discountValue: 2000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(13000); // 15000 - 2000
      expect(response.body.discountAmount).toBe(2000);
      expect(response.body.totalAmount).toBe(13000);
      expect(response.body.changeAmount).toBe(2000);
    });

    it('should create sale with tax calculation', async () => {
      const saleData = {
        customerName: 'Tax Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        taxAmount: 1650, // 11% tax
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(15000);
      expect(response.body.taxAmount).toBe(1650);
      expect(response.body.totalAmount).toBe(16650); // 15000 + 1650
      expect(response.body.changeAmount).toBe(3350); // 20000 - 16650
    });

    it('should auto-generate sale number if not provided', async () => {
      const saleData = {
        customerName: 'Auto Number Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.saleNumber).toBeDefined();
      expect(response.body.saleNumber).toMatch(/^TRX-\d{8}-\d{3}$/);
    });

    it('should accept custom sale number', async () => {
      const saleData = {
        saleNumber: 'CUSTOM-TRX-001',
        customerName: 'Custom Number Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.saleNumber).toBe('CUSTOM-TRX-001');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .post('/api/sales')
        .send({
          customerName: 'Test',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
        });

      expectUnauthorized(response);
    });

    it('should fail with duplicate sale number', async () => {
      const saleData = {
        saleNumber: 'CUSTOM-TRX-001', // Already exists
        customerName: 'Duplicate Number Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Nomor transaksi sudah digunakan');
    });

    it('should fail with insufficient payment', async () => {
      const saleData = {
        customerName: 'Insufficient Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: 5000, // Less than total
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Jumlah pembayaran tidak mencukupi');
    });

    it('should fail with non-existent product', async () => {
      const saleData = {
        customerName: 'Non-existent Product Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: 'non-existent-product-id',
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Stok tidak mencukupi');
    });

    it('should fail with insufficient stock', async () => {
      const saleData = {
        customerName: 'Insufficient Stock Customer',
        paymentMethod: 'CASH',
        amountPaid: 100000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 200, // More than available stock (100)
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Stok tidak mencukupi');
    });

    it('should fail with missing required fields', async () => {
      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({});

      expectValidationError(response);
    });

    it('should fail with invalid payment method', async () => {
      const saleData = {
        customerName: 'Invalid Payment Customer',
        paymentMethod: 'INVALID_METHOD',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with negative quantity', async () => {
      const saleData = {
        customerName: 'Negative Quantity Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: -1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with negative unit price', async () => {
      const saleData = {
        customerName: 'Negative Price Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: -7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with negative amount paid', async () => {
      const saleData = {
        customerName: 'Negative Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: -10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with empty items array', async () => {
      const saleData = {
        customerName: 'Empty Items Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with invalid date format', async () => {
      const saleData = {
        customerName: 'Invalid Date Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        saleDate: 'invalid-date',
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });
  });

  describe('GET /api/sales', () => {
    beforeAll(async () => {
      // Create additional test sales for filtering tests
      const sales = [
        {
          customerName: 'Filter Test Customer 1',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
        },
        {
          customerName: 'Filter Test Customer 2',
          paymentMethod: 'TRANSFER',
          amountPaid: 15000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 2, unitPrice: 7500 }],
        },
        {
          customerId: testCustomerId,
          paymentMethod: 'CASH',
          amountPaid: 8000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
        },
      ];

      for (const sale of sales) {
        await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(sale);
      }
    });

    it('should list all sales for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.meta).toBeDefined();
      expect(response.body.meta.total).toBeGreaterThan(0);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(10);

      // Check that sales include related data
      const sale = response.body.data[0];
      expect(sale.cashier).toBeDefined();
      expect(sale.saleItems).toBeDefined();
      expect(sale.saleItems).toBeInstanceOf(Array);
    });

    it('should filter sales by payment method', async () => {
      const response = await ctx.request
        .get('/api/sales?paymentMethod=TRANSFER')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.paymentMethod === 'TRANSFER')).toBe(true);
    });

    it('should filter sales by status', async () => {
      const response = await ctx.request
        .get('/api/sales?status=COMPLETED')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.status === 'COMPLETED')).toBe(true);
    });

    it('should filter sales by customer', async () => {
      const response = await ctx.request
        .get(`/api/sales?customerId=${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.customerId === testCustomerId)).toBe(true);
    });

    it('should filter sales by cashier', async () => {
      const response = await ctx.request
        .get(`/api/sales?cashierId=${ctx.users.admin.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.cashierId === ctx.users.admin.id)).toBe(true);
    });

    it('should search sales by sale number', async () => {
      const response = await ctx.request
        .get('/api/sales?search=TRX')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((s: any) => s.saleNumber.includes('TRX'))).toBe(true);
    });

    it('should search sales by customer name', async () => {
      const response = await ctx.request
        .get('/api/sales?search=Filter Test Customer')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((s: any) => s.customerName && s.customerName.includes('Filter Test Customer'))).toBe(true);
    });

    it('should filter sales by date range', async () => {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const response = await ctx.request
        .get(`/api/sales?startDate=${yesterday.toISOString()}&endDate=${tomorrow.toISOString()}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should paginate results', async () => {
      const response = await ctx.request
        .get('/api/sales?page=1&limit=2')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should sort sales', async () => {
      const response = await ctx.request
        .get('/api/sales?sortBy=totalAmount&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const amounts = response.body.data.map((s: any) => parseFloat(s.totalAmount));
      const sortedAmounts = [...amounts].sort((a, b) => a - b);
      expect(amounts).toEqual(sortedAmounts);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales');

      expectUnauthorized(response);
    });

    it('should validate pagination parameters', async () => {
      const response = await ctx.request
        .get('/api/sales?page=0&limit=101')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectValidationError(response);
    });
  });

  describe('GET /api/sales/stats', () => {
    it('should return sales statistics for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/sales/stats')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('totalSales');
      expect(response.body).toHaveProperty('todaySales');
      expect(response.body).toHaveProperty('completedSales');
      expect(response.body).toHaveProperty('totalRevenue');
      expect(response.body).toHaveProperty('todayRevenue');
      expect(typeof response.body.totalSales).toBe('number');
      expect(typeof response.body.todaySales).toBe('number');
      expect(typeof response.body.completedSales).toBe('number');
      expect(typeof response.body.totalRevenue).toBe('number');
      expect(typeof response.body.todayRevenue).toBe('number');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales/stats');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/sales/generate-number', () => {
    it('should generate sale number for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/sales/generate-number')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.saleNumber).toBeDefined();
      expect(response.body.saleNumber).toMatch(/^TRX-\d{8}-\d{3}$/);
    });

    it('should generate unique sale numbers', async () => {
      const response1 = await ctx.request
        .get('/api/sales/generate-number')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 100));

      const response2 = await ctx.request
        .get('/api/sales/generate-number')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response1);
      expectSuccess(response2);
      expect(response1.body.saleNumber).not.toBe(response2.body.saleNumber);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales/generate-number');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/sales/validate-number/:number', () => {
    it('should validate unique sale number', async () => {
      const response = await ctx.request
        .get('/api/sales/validate-number/UNIQUE-TRX-123')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should validate existing sale number', async () => {
      const response = await ctx.request
        .get('/api/sales/validate-number/CUSTOM-TRX-001')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(false);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales/validate-number/TEST-123');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/sales/:id', () => {
    it('should return sale details for authenticated user', async () => {
      const response = await ctx.request
        .get(`/api/sales/${testSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(testSaleId);
      expect(response.body.customerId).toBe(testCustomerId);
      expect(response.body.cashier).toBeDefined();
      expect(response.body.customer).toBeDefined();
      expect(response.body.saleItems).toBeDefined();
      expect(response.body.saleItems).toBeInstanceOf(Array);
      expect(response.body.saleItems.length).toBeGreaterThan(0);

      // Check sale item details
      const saleItem = response.body.saleItems[0];
      expect(saleItem.product).toBeDefined();
      expect(saleItem.unit).toBeDefined();
      expect(saleItem.quantity).toBeDefined();
      expect(saleItem.unitPrice).toBeDefined();
      expect(saleItem.totalPrice).toBeDefined();
    });

    it('should fail with non-existent sale ID', async () => {
      const response = await ctx.request
        .get('/api/sales/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
      expect(response.body.message).toContain('Transaksi tidak ditemukan');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/sales/${testSaleId}`);

      expectUnauthorized(response);
    });
  });

  describe('Payment Method Tests', () => {
    it('should create sale with CASH payment', async () => {
      const saleData = {
        customerName: 'Cash Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('CASH');
    });

    it('should create sale with TRANSFER payment', async () => {
      const saleData = {
        customerName: 'Transfer Payment Customer',
        paymentMethod: 'TRANSFER',
        amountPaid: 7500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('TRANSFER');
      expect(response.body.changeAmount).toBe(0); // Exact payment
    });

    it('should create sale with CREDIT payment', async () => {
      const saleData = {
        customerName: 'Credit Payment Customer',
        paymentMethod: 'CREDIT',
        amountPaid: 7500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('CREDIT');
    });

    it('should create sale with GIRO payment', async () => {
      const saleData = {
        customerName: 'Giro Payment Customer',
        paymentMethod: 'GIRO',
        amountPaid: 7500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('GIRO');
    });
  });

  describe('Discount Calculation Tests', () => {
    it('should calculate percentage discount correctly', async () => {
      const saleData = {
        customerName: 'Percentage Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        discountType: 'PERCENTAGE',
        discountValue: 20, // 20% discount
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 2, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(12000); // 15000 - (15000 * 0.2)
      expect(response.body.discountAmount).toBe(3000); // 15000 * 0.2
      expect(response.body.totalAmount).toBe(12000);
    });

    it('should calculate fixed amount discount correctly', async () => {
      const saleData = {
        customerName: 'Fixed Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        discountType: 'FIXED_AMOUNT',
        discountValue: 2500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 2, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(12500); // 15000 - 2500
      expect(response.body.discountAmount).toBe(2500);
      expect(response.body.totalAmount).toBe(12500);
    });

    it('should calculate item-level percentage discount correctly', async () => {
      const saleData = {
        customerName: 'Item Percentage Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'PERCENTAGE',
            discountValue: 15, // 15% item discount
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(12750); // 15000 - (15000 * 0.15)
      expect(response.body.totalAmount).toBe(12750);
    });

    it('should calculate item-level fixed discount correctly', async () => {
      const saleData = {
        customerName: 'Item Fixed Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'FIXED_AMOUNT',
            discountValue: 1000, // 1000 item discount
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(14000); // 15000 - 1000
      expect(response.body.totalAmount).toBe(14000);
    });
  });

  describe('Inventory Integration Tests', () => {
    it('should allocate stock correctly during sale', async () => {
      // Check initial inventory state
      const initialInventory = await ctx.request
        .get(`/api/inventory?productId=${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const initialStock = initialInventory.body.data.reduce((sum: number, item: any) => sum + (item.quantityOnHand - item.quantityAllocated), 0);

      const saleData = {
        customerName: 'Stock Allocation Customer',
        paymentMethod: 'CASH',
        amountPaid: 40000, // 5 * 7500 = 37500, paying 40000
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 5, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);

      // Check that stock was allocated
      const finalInventory = await ctx.request
        .get(`/api/inventory?productId=${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Calculate available stock (quantityOnHand - quantityAllocated) for both initial and final
      // With our updated logic, both quantityOnHand is reduced and quantityAllocated is increased
      const finalAvailableStock = finalInventory.body.data.reduce((sum: number, item: any) => sum + (item.quantityOnHand - item.quantityAllocated), 0);
      
      // Stock is reduced by 5 in the test because we're buying 5 items
      expect(finalAvailableStock).toBe(initialStock - 5);
    });

    it('should use FEFO allocation for medicine products', async () => {
      // Create a medicine product
      const medicineBaseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
      const medicineProduct = await testSetup.createTestProduct(medicineBaseUnit.id, ctx.users.admin.id, 'MEDICINE');

      // Create inventory items with different expiry dates
      const earlyBatchResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: medicineProduct.id,
          unitId: medicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-EARLY',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          expiryDate: '2025-06-30', // Earlier expiry (but still in future)
          location: 'Rak A-1',
        });

      expectSuccess(earlyBatchResponse, 201);

      const lateBatchResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: medicineProduct.id,
          unitId: medicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-LATE',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          expiryDate: '2025-12-31', // Later expiry
          location: 'Rak A-2',
        });

      expectSuccess(lateBatchResponse, 201);

      const saleData = {
        customerName: 'FEFO Test Customer',
        paymentMethod: 'CASH',
        amountPaid: 80000, // 10 * 7500 = 75000, paying 80000
        items: [{ productId: medicineProduct.id, unitId: medicineBaseUnit.id, quantity: 10, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);

      // Verify FEFO allocation by checking which inventory items were allocated from
      // Get all inventory items for this medicine product
      const inventoryItems = await ctx.request
        .get(`/api/inventory?productId=${medicineProduct.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(inventoryItems);
      expect(inventoryItems.body.data.length).toBe(2); // Should have 2 batches

      // Find the batches by their batch numbers
      const earlyBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-EARLY');
      const lateBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-LATE');

      expect(earlyBatch).toBeDefined();
      expect(lateBatch).toBeDefined();

      // Check stock movements for both batches to verify FEFO allocation
      const earlyBatchMovements = await ctx.request
        .get(`/api/inventory/${earlyBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const lateBatchMovements = await ctx.request
        .get(`/api/inventory/${lateBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(earlyBatchMovements);
      expectSuccess(lateBatchMovements);

      // Find allocation movements (sales create ALLOCATION type movements)
      const earlyAllocationMovements = earlyBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );
      const lateAllocationMovements = lateBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );

      // FEFO should prioritize the earlier expiry batch (BATCH-EARLY)
      // Since we're buying 10 items and each batch has 50, only the early batch should be allocated from
      expect(earlyAllocationMovements.length).toBeGreaterThan(0);
      expect(earlyAllocationMovements[0].quantity).toBe(10);

      // Late batch should not be allocated from since early batch has enough stock
      expect(lateAllocationMovements.length).toBe(0);

      // Verify the sale was created successfully
      expect(response.body.id).toBeDefined();
      expect(response.body.totalAmount).toBe(75000);
    });

    it('should use FIFO allocation for non-medicine products', async () => {
      // Create a non-medicine product
      const nonMedicineBaseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
      const nonMedicineProduct = await testSetup.createTestProduct(nonMedicineBaseUnit.id, ctx.users.admin.id, 'SUPPLEMENT');

      // Create inventory items with different received dates
      await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: nonMedicineProduct.id,
          unitId: nonMedicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-OLD',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          receivedDate: '2024-01-01', // Earlier received
          location: 'Rak B-1',
        });

      await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: nonMedicineProduct.id,
          unitId: nonMedicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-NEW',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          receivedDate: '2024-06-01', // Later received
          location: 'Rak B-2',
        });

      const saleData = {
        customerName: 'FIFO Test Customer',
        paymentMethod: 'CASH',
        amountPaid: 80000, // 10 * 7500 = 75000, paying 80000
        items: [{ productId: nonMedicineProduct.id, unitId: nonMedicineBaseUnit.id, quantity: 10, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);

      // Verify FIFO allocation by checking which inventory items were allocated from
      // Get all inventory items for this non-medicine product
      const inventoryItems = await ctx.request
        .get(`/api/inventory?productId=${nonMedicineProduct.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(inventoryItems);
      expect(inventoryItems.body.data.length).toBe(2); // Should have 2 batches

      // Find the batches by their batch numbers
      const oldBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-OLD');
      const newBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-NEW');

      expect(oldBatch).toBeDefined();
      expect(newBatch).toBeDefined();

      // Check stock movements for both batches to verify FIFO allocation
      const oldBatchMovements = await ctx.request
        .get(`/api/inventory/${oldBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const newBatchMovements = await ctx.request
        .get(`/api/inventory/${newBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(oldBatchMovements);
      expectSuccess(newBatchMovements);

      // Find allocation movements (sales create ALLOCATION type movements)
      const oldAllocationMovements = oldBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );
      const newAllocationMovements = newBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );

      // FIFO should prioritize the earlier received batch (BATCH-OLD)
      // Since we're buying 10 items and each batch has 50, only the old batch should be allocated from
      expect(oldAllocationMovements.length).toBeGreaterThan(0);
      expect(oldAllocationMovements[0].quantity).toBe(10);

      // New batch should not be allocated from since old batch has enough stock
      expect(newAllocationMovements.length).toBe(0);

      // Verify the sale was created successfully
      expect(response.body.id).toBeDefined();
      expect(response.body.totalAmount).toBe(75000);
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle very large quantities', async () => {
      // Create inventory with large quantity
      await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: testProductId,
          unitId: testUnitId,
          supplierId: testSupplierId,
          batchNumber: 'LARGE-BATCH',
          quantityOnHand: 10000,
          costPrice: 5000,
          sellingPrice: 7500,
          location: 'Rak C-1',
        });

      const saleData = {
        customerName: 'Large Quantity Customer',
        paymentMethod: 'CASH',
        amountPaid: 1000000,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 100, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(750000); // 100 * 7500
    });

    it('should handle very large amounts', async () => {
      const saleData = {
        customerName: 'Large Amount Customer',
        paymentMethod: 'TRANSFER',
        amountPaid: 999999.99,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 999999.99 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.totalAmount).toBe(999999.99);
      expect(response.body.changeAmount).toBe(0);
    });

    it('should handle decimal quantities correctly', async () => {
      const saleData = {
        customerName: 'Decimal Quantity Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500.50 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(7500.50);
      expect(response.body.totalAmount).toBe(7500.50);
      expect(response.body.changeAmount).toBe(2499.50);
    });

    it('should handle special characters in customer data', async () => {
      const saleData = {
        customerName: 'José María Ñoño',
        customerPhone: '+62 (812) 3456-7890',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        notes: 'Special chars: àáâãäåæçèéêë',
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.customerName).toBe('José María Ñoño');
      expect(response.body.customerPhone).toBe('+62 (812) 3456-7890');
      expect(response.body.notes).toBe('Special chars: àáâãäåæçèéêë');
    });

    it('should handle empty search queries gracefully', async () => {
      const response = await ctx.request
        .get('/api/sales?search=')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should handle large page numbers gracefully', async () => {
      const response = await ctx.request
        .get('/api/sales?page=999999&limit=10')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBe(0);
      expect(response.body.meta.page).toBe(999999);
    });

    it('should handle exact payment amounts', async () => {
      const saleData = {
        customerName: 'Exact Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: 7500, // Exact amount
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.changeAmount).toBe(0);
    });

    it('should handle sales with notes', async () => {
      const saleData = {
        customerName: 'Notes Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        notes: 'Customer requested special packaging',
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
            notes: 'Handle with care',
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.notes).toBe('Customer requested special packaging');
    });

    it('should handle future sale dates', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const saleData = {
        customerName: 'Future Date Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        saleDate: futureDate.toISOString(),
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(new Date(response.body.saleDate).getTime()).toBe(futureDate.getTime());
    });
  });

  // Enhanced Transaction Processing Tests
  describe('Enhanced Transaction Processing', () => {
    let draftSaleId: string;

    describe('POST /api/sales/draft', () => {
      it('should verify draft endpoint exists', async () => {
        // First, let's test with a simple GET to see what happens
        const getResponse = await ctx.request
          .get('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        // Now test the actual POST
        const postResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({});

        // The endpoint should exist (not 404), even if it fails validation
        expect(postResponse.status).not.toBe(404);
      });

      it('should create draft sale without stock allocation', async () => {
        const saleData = {
          customerName: 'Draft Sale Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.customerName).toBe('Draft Sale Customer');
        expect(response.body.subtotal).toBe(7500);
        expect(response.body.totalAmount).toBe(7500);
        expect(response.body.changeAmount).toBe(2500);
        expect(response.body.saleNumber).toBeDefined();

        draftSaleId = response.body.id;
      });

      it('should create draft sale with registered customer', async () => {
        const saleData = {
          customerId: testCustomerId,
          paymentMethod: 'TRANSFER',
          amountPaid: 15000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.customerId).toBe(testCustomerId);
        expect(response.body.subtotal).toBe(15000);
        expect(response.body.paymentMethod).toBe('TRANSFER');
      });

      it('should create draft sale with discounts', async () => {
        const saleData = {
          customerName: 'Draft Discount Customer',
          paymentMethod: 'CASH',
          amountPaid: 20000,
          discountType: 'PERCENTAGE',
          discountValue: 10,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 7500,
              discountType: 'FIXED',
              discountValue: 500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.discountAmount).toBeGreaterThan(0);
      });

      it('should allow draft sale even with insufficient stock (warning only)', async () => {
        const saleData = {
          customerName: 'High Quantity Draft Customer',
          paymentMethod: 'CASH',
          amountPaid: 1000000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 500, // More than available stock
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.subtotal).toBe(3750000); // 500 * 7500
      });

      it('should allow draft sale with insufficient payment (warning only)', async () => {
        const saleData = {
          customerName: 'Insufficient Payment Draft Customer',
          paymentMethod: 'CASH',
          amountPaid: 5000, // Less than total (7500)
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        // Draft sales should allow insufficient payment
        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.totalAmount).toBe(7500);
        expect(response.body.amountPaid).toBe(5000);
        expect(response.body.changeAmount).toBe(-2500); // Negative change indicates insufficient payment
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .post('/api/sales/draft')
          .send({
            customerName: 'Test',
            paymentMethod: 'CASH',
            amountPaid: 10000,
            items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
          });

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id/complete', () => {
      it('should complete draft sale with stock allocation', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${draftSaleId}/complete`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);
        expect(response.body.status).toBe('COMPLETED');
        expect(response.body.id).toBe(draftSaleId);
      });

      it('should fail to complete already completed sale', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${draftSaleId}/complete`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectConflict(response);
        expect(response.body.message).toContain('Hanya transaksi draft yang dapat diselesaikan');
      });

      it('should fail to complete sale with insufficient stock', async () => {
        // First, check current available stock to ensure we use a quantity that exceeds it
        const inventoryResponse = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const availableStock = inventoryResponse.body.data.reduce(
          (sum: number, item: any) => sum + (item.quantityOnHand - item.quantityAllocated),
          0
        );

        // Use a quantity that definitely exceeds available stock
        const excessiveQuantity = availableStock + 50;

        // Create a draft sale with excessive quantity
        const draftData = {
          customerName: 'High Stock Draft Customer',
          paymentMethod: 'CASH',
          amountPaid: 1000000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: excessiveQuantity, // Definitely more than available stock
              unitPrice: 7500,
            },
          ],
        };

        const draftResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        expectSuccess(draftResponse, 201);

        // Try to complete it - this should fail due to insufficient stock
        const completeResponse = await ctx.request
          .patch(`/api/sales/${draftResponse.body.id}/complete`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expect(completeResponse.status).toBe(400);
        expect(completeResponse.body.message).toContain('Stok tidak mencukupi');
      });

      it('should fail with non-existent sale ID', async () => {
        const response = await ctx.request
          .patch('/api/sales/non-existent-id/complete')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectNotFound(response);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${draftSaleId}/complete`);

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id/cancel', () => {
      let cancelTestSaleId: string;

      beforeAll(async () => {
        // Create a completed sale for cancellation tests
        const saleData = {
          customerName: 'Cancel Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);
        
        expectSuccess(saleResponse, 201);
        cancelTestSaleId = saleResponse.body.id;
      });

      it('should cancel completed sale', async () => {
        // Create a completed sale for cancellation tests
        const saleData = {
          customerName: 'Cancel Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);
        
        expectSuccess(saleResponse, 201);
        const saleId = saleResponse.body.id;

        // Cancel the sale
        const response = await ctx.request
          .patch(`/api/sales/${saleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Customer requested cancellation' });

        expectSuccess(response);
        expect(response.body.status).toBe('CANCELLED');
        expect(response.body.notes).toContain('Dibatalkan: Customer requested cancellation');
      });

      it('should restore stock when cancelling completed sale', async () => {
        // Create a new completed sale for stock restoration testing
        const saleData = {
          customerName: 'Stock Restoration Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 15000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        const saleId = saleResponse.body.id;

        // Get inventory before cancellation
        const inventoryBefore = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        // Cancel the sale
        const cancelResponse = await ctx.request
          .patch(`/api/sales/${saleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Stock restoration test' });

        expectSuccess(cancelResponse);

        // Get inventory after cancellation
        const inventoryAfter = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        // Verify stock movement was created for deallocation
        // Check all inventory items for the product since deallocation might happen on any item
        let allDeallocationMovements: any[] = [];

        for (const inventoryItem of inventoryAfter.body.data) {
          const stockMovements = await ctx.request
            .get(`/api/inventory/${inventoryItem.id}/stock-movements`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(stockMovements);

          // Find deallocation movements for this item
          const itemDeallocationMovements = stockMovements.body.data.filter((movement: any) =>
            movement.type === 'ALLOCATION' &&
            movement.quantity < 0 &&
            movement.referenceType === ReferenceType.SALE_CANCELLATION &&
            movement.referenceId === saleId
          );

          allDeallocationMovements.push(...itemDeallocationMovements);
        }

        expect(allDeallocationMovements.length).toBeGreaterThan(0);

        // Verify total deallocated quantity
        const totalDeallocated = allDeallocationMovements.reduce((sum: number, movement: any) => sum + Math.abs(movement.quantity), 0);
        expect(totalDeallocated).toBe(2);

        // Verify movement details
        expect(allDeallocationMovements[0].referenceId).toBe(saleId);
        expect(allDeallocationMovements[0].reason).toContain('Dealokasi stok untuk pembatalan transaksi');
      });

      it('should fail to cancel already cancelled sale', async () => {
        // First cancel the sale
        const firstCancelResponse = await ctx.request
          .patch(`/api/sales/${cancelTestSaleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'First cancellation' });
        
        expectSuccess(firstCancelResponse);
        
        // Try to cancel it again
        const response = await ctx.request
          .patch(`/api/sales/${cancelTestSaleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Another reason' });

        expectConflict(response);
        expect(response.body.message).toContain('Transaksi sudah dibatalkan');
      });

      it('should cancel draft sale', async () => {
        // Create a draft sale
        const draftData = {
          customerName: 'Draft Cancel Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const draftResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        // Cancel the draft
        const cancelResponse = await ctx.request
          .patch(`/api/sales/${draftResponse.body.id}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Draft cancellation' });

        expectSuccess(cancelResponse);
        expect(cancelResponse.body.status).toBe('CANCELLED');
      });

      it('should fail with non-existent sale ID', async () => {
        const response = await ctx.request
          .patch('/api/sales/non-existent-id/cancel')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Test' });

        expectNotFound(response);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${cancelTestSaleId}/cancel`)
          .send({ reason: 'Test' });

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id/refund', () => {
      let refundTestSaleId: string;

      beforeAll(async () => {
        // Create a completed sale for refund tests
        const saleData = {
          customerName: 'Refund Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(saleData);

        refundTestSaleId = response.body.id;
      });

      it('should refund completed sale as manager', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${refundTestSaleId}/refund`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ reason: 'Product defect' });

        expectSuccess(response);
        expect(response.body.status).toBe('REFUNDED');
        expect(response.body.notes).toContain('Di-refund: Product defect');
      });

      it('should restore stock when refunding completed sale', async () => {
        // Create a new completed sale for refund stock restoration testing
        const saleData = {
          customerName: 'Refund Stock Restoration Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 22500,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 3,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(saleData);

        const saleId = saleResponse.body.id;

        // Get inventory before refund
        const inventoryBefore = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const allocatedBefore = inventoryBefore.body.data.reduce((sum: number, item: any) => sum + item.quantityAllocated, 0);

        // Refund the sale
        const refundResponse = await ctx.request
          .patch(`/api/sales/${saleId}/refund`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ reason: 'Stock restoration test refund' });

        expectSuccess(refundResponse);
        expect(refundResponse.body.status).toBe('REFUNDED');
      });
    });
  });
});
