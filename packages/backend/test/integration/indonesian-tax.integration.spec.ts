import { IntegrationTestSetup, TestContext } from './test-setup';
import { TaxConfigurationService } from '../../src/procurement/services/tax-configuration.service';
import { IndonesianTaxService } from '../../src/procurement/services/indonesian-tax.service';
import { TaxType, TaxEntityType } from '../../src/procurement/dto/tax-configuration.dto';
import { Prisma } from '@prisma/client';

describe('Indonesian Tax Service Integration Tests', () => {
  let testContext: TestContext;
  let testSetup: IntegrationTestSetup;
  let taxConfigService: TaxConfigurationService;
  let indonesianTaxService: IndonesianTaxService;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    testContext = await testSetup.setup();

    // Initialize services
    taxConfigService = new TaxConfigurationService(testContext.prisma);
    indonesianTaxService = new IndonesianTaxService(testContext.prisma, taxConfigService);
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  beforeEach(async () => {
    // Clean up tax settings before each test
    await testContext.prisma.appSettings.deleteMany({
      where: {
        settingKey: {
          startsWith: 'tax.',
        },
      },
    });

    // Set up standard PPN configuration
    await taxConfigService.updatePPNConfiguration({
      taxRate: 11,
      isActive: true,
      effectiveFrom: new Date('2024-01-01'),
      effectiveTo: new Date('2024-12-31'),
    });
  });

  describe('Applicable Tax Types Determination', () => {
    it('should determine correct tax types for small individual pharmacy', async () => {
      const applicableTaxes = await indonesianTaxService.getApplicableTaxTypes(
        TaxEntityType.INDIVIDUAL,
        1_000_000_000, // Rp 1 billion
        2, // 2 years in operation
        true // Has employees
      );

      expect(applicableTaxes).toContain(TaxType.PP_23); // Should use PP 23
      expect(applicableTaxes).toContain(TaxType.PPH_21); // Employee tax
      expect(applicableTaxes).not.toContain(TaxType.PPN); // Below PKP threshold
    });

    it('should determine correct tax types for large individual pharmacy', async () => {
      const applicableTaxes = await indonesianTaxService.getApplicableTaxTypes(
        TaxEntityType.INDIVIDUAL,
        5_000_000_000, // Rp 5 billion (above PKP threshold)
        8, // 8 years in operation (beyond PP 23 limit)
        true // Has employees
      );

      expect(applicableTaxes).toContain(TaxType.PPH_25); // Should use PPh 25
      expect(applicableTaxes).toContain(TaxType.PPN); // Above PKP threshold
      expect(applicableTaxes).toContain(TaxType.PPH_21); // Employee tax
      expect(applicableTaxes).not.toContain(TaxType.PP_23); // Beyond time limit
    });

    it('should determine correct tax types for new corporate pharmacy', async () => {
      const applicableTaxes = await indonesianTaxService.getApplicableTaxTypes(
        TaxEntityType.CORPORATE,
        2_000_000_000, // Rp 2 billion
        2, // 2 years in operation
        true // Has employees
      );

      expect(applicableTaxes).toContain(TaxType.PP_23); // First 3 years for corporate
      expect(applicableTaxes).toContain(TaxType.PPH_21); // Employee tax
      expect(applicableTaxes).not.toContain(TaxType.PPN); // Below PKP threshold
    });

    it('should determine correct tax types for established corporate pharmacy', async () => {
      const applicableTaxes = await indonesianTaxService.getApplicableTaxTypes(
        TaxEntityType.CORPORATE,
        6_000_000_000, // Rp 6 billion
        5, // 5 years in operation
        true // Has employees
      );

      expect(applicableTaxes).toContain(TaxType.PPH_25); // Beyond 3 years
      expect(applicableTaxes).toContain(TaxType.PPN); // Above PKP threshold
      expect(applicableTaxes).toContain(TaxType.PPH_21); // Employee tax
      expect(applicableTaxes).not.toContain(TaxType.PP_23); // Beyond time limit
    });
  });

  describe('PP 23 Tax Calculation', () => {
    it('should calculate PP 23 for individual correctly', async () => {
      const result = await indonesianTaxService.calculatePP23Tax(
        100_000_000, // Rp 100 million monthly revenue
        TaxEntityType.INDIVIDUAL,
        1_200_000_000 // Rp 1.2 billion annual revenue
      );

      // For individuals, deduct Rp 500M annually (Rp 41.67M monthly)
      const expectedTaxableRevenue = 100_000_000 - (500_000_000 / 12);
      const expectedTaxAmount = expectedTaxableRevenue * 0.005; // 0.5%

      expect(result.taxableRevenue).toBeCloseTo(expectedTaxableRevenue, 0);
      expect(result.taxAmount).toBeCloseTo(expectedTaxAmount, 0);
      expect(result.taxRate).toBe(0.5);
    });

    it('should calculate PP 23 for corporate correctly', async () => {
      const result = await indonesianTaxService.calculatePP23Tax(
        100_000_000, // Rp 100 million monthly revenue
        TaxEntityType.CORPORATE,
        1_200_000_000 // Rp 1.2 billion annual revenue
      );

      // For corporate, no deduction
      const expectedTaxAmount = 100_000_000 * 0.005; // 0.5%

      expect(result.taxableRevenue).toBe(100_000_000);
      expect(result.taxAmount).toBe(expectedTaxAmount);
      expect(result.taxRate).toBe(0.5);
    });

    it('should handle individual with revenue below threshold', async () => {
      const result = await indonesianTaxService.calculatePP23Tax(
        30_000_000, // Rp 30 million monthly revenue (below threshold)
        TaxEntityType.INDIVIDUAL,
        360_000_000 // Rp 360 million annual revenue
      );

      // Should result in zero taxable revenue
      expect(result.taxableRevenue).toBe(0);
      expect(result.taxAmount).toBe(0);
      expect(result.taxRate).toBe(0.5);
    });
  });

  describe('PPh 25 Tax Calculation', () => {
    it('should calculate PPh 25 for individual correctly', async () => {
      const result = await indonesianTaxService.calculatePPh25Tax(
        200_000_000, // Rp 200 million monthly revenue
        TaxEntityType.INDIVIDUAL
      );

      const expectedInstallment = 200_000_000 * 0.0075; // 0.75%

      expect(result.monthlyInstallment).toBe(expectedInstallment);
      expect(result.taxRate).toBe(0.75);
      expect(result.calculationBasis).toBe('Monthly Revenue');
    });

    it('should calculate PPh 25 for corporate correctly', async () => {
      const previousYearTaxDue = 120_000_000; // Rp 120 million

      const result = await indonesianTaxService.calculatePPh25Tax(
        200_000_000, // Monthly revenue (not used for corporate)
        TaxEntityType.CORPORATE,
        previousYearTaxDue
      );

      const expectedInstallment = previousYearTaxDue / 12; // 1/12 of previous year

      expect(result.monthlyInstallment).toBe(expectedInstallment);
      expect(result.taxRate).toBe(22);
      expect(result.calculationBasis).toBe('Previous Year Tax Due');
    });

    it('should handle corporate with no previous year tax due', async () => {
      // Should throw an error when previousYearTaxDue is missing for corporate
      await expect(
        indonesianTaxService.calculatePPh25Tax(
          200_000_000,
          TaxEntityType.CORPORATE
          // No previousYearTaxDue provided
        )
      ).rejects.toThrow('Data pajak terutang tahun sebelumnya diperlukan untuk kalkulasi PPh 25 badan usaha');
    });
  });

  describe('Indonesian PPN Calculation', () => {
    it('should calculate PPN with Indonesian-specific rules', async () => {
      const result = await indonesianTaxService.calculateIndonesianPPN(
        1_000_000, // Rp 1 million
        true // Retail transaction
      );

      expect(result.subtotal).toBe(1_000_000);
      expect(result.ppnAmount).toBe(110_000); // 11% of 1 million
      expect(result.totalAmount).toBe(1_110_000);
      expect(result.ppnRate).toBe(11);
      expect(result.requiresFakturPajak).toBe(true);
    });

    it('should handle inactive PPN', async () => {
      // Deactivate PPN
      await taxConfigService.updatePPNConfiguration({
        isActive: false,
      });

      const result = await indonesianTaxService.calculateIndonesianPPN(
        1_000_000,
        true
      );

      expect(result.subtotal).toBe(1_000_000);
      expect(result.ppnAmount).toBe(0);
      expect(result.totalAmount).toBe(1_000_000);
      expect(result.ppnRate).toBe(0);
      expect(result.requiresFakturPajak).toBe(false);
    });
  });

  describe('Tax Compliance Status', () => {
    it('should provide correct compliance status for small pharmacy', async () => {
      const result = await indonesianTaxService.getTaxComplianceStatus(
        TaxEntityType.INDIVIDUAL,
        1_000_000_000, // Rp 1 billion
        2, // 2 years
        true // Has employees
      );

      expect(result.requiredRegistrations).toContain('NPWP (Nomor Pokok Wajib Pajak)');
      expect(result.requiredRegistrations).not.toContain('PKP (Pengusaha Kena Pajak)');
      expect(result.applicableTaxes).toContain(TaxType.PP_23);
      expect(result.applicableTaxes).toContain(TaxType.PPH_21);

      const npwpRequirement = result.complianceChecklist.find(
        item => item.requirement === 'Pendaftaran NPWP'
      );
      expect(npwpRequirement?.status).toBe('required');
    });

    it('should provide correct compliance status for large pharmacy', async () => {
      const result = await indonesianTaxService.getTaxComplianceStatus(
        TaxEntityType.CORPORATE,
        6_000_000_000, // Rp 6 billion
        5, // 5 years
        true // Has employees
      );

      expect(result.requiredRegistrations).toContain('NPWP (Nomor Pokok Wajib Pajak)');
      expect(result.requiredRegistrations).toContain('PKP (Pengusaha Kena Pajak)');
      expect(result.applicableTaxes).toContain(TaxType.PPH_25);
      expect(result.applicableTaxes).toContain(TaxType.PPN);
      expect(result.applicableTaxes).toContain(TaxType.PPH_21);

      const pkpRequirement = result.complianceChecklist.find(
        item => item.requirement === 'Pendaftaran PKP'
      );
      expect(pkpRequirement?.status).toBe('required');

      const eFakturRequirement = result.complianceChecklist.find(
        item => item.requirement === 'Aplikasi e-Faktur'
      );
      expect(eFakturRequirement?.status).toBe('required');
    });
  });

  describe('Tax Configuration Validation', () => {
    it('should validate PPN rate correctly', async () => {
      const validResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PPN,
        11
      );

      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      const invalidResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PPN,
        15 // Invalid rate
      );

      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Tarif PPN harus 11% (saat ini) atau 12% (mulai 2025)');
    });

    it('should validate PP 23 rate correctly', async () => {
      const validResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PP_23,
        0.5
      );

      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      const invalidResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PP_23,
        1.0 // Invalid rate
      );

      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Tarif PP 23 harus 0,5% dari omzet');
    });

    it('should validate PPh 25 rates correctly', async () => {
      const validIndividualResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PPH_25,
        0.75,
        TaxEntityType.INDIVIDUAL
      );

      expect(validIndividualResult.isValid).toBe(true);

      const validCorporateResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PPH_25,
        22,
        TaxEntityType.CORPORATE
      );

      expect(validCorporateResult.isValid).toBe(true);

      const invalidResult = await indonesianTaxService.validateTaxConfiguration(
        TaxType.PPH_25,
        5, // Invalid rate
        TaxEntityType.INDIVIDUAL
      );

      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Tarif PPh 25 untuk perorangan harus 0,75% dari omzet');
    });
  });

  describe('Tax Calendar', () => {
    it('should provide correct tax calendar', () => {
      const calendar = indonesianTaxService.getTaxCalendar();

      expect(calendar).toHaveLength(5);

      const pphPayment = calendar.find(item =>
        item.taxType === TaxType.PPH_21 && item.description.includes('pembayaran')
      );
      expect(pphPayment?.deadline).toBe('Tanggal 10 bulan berikutnya');

      const ppnDeadline = calendar.find(item => item.taxType === TaxType.PPN);
      expect(ppnDeadline?.deadline).toBe('Akhir bulan berikutnya');
    });
  });
});
