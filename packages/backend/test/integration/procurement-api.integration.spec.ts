import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectSuccess } from './test-setup';

describe('Procurement API Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testSupplier: any;
  let testProduct: any;
  let testUnit: any;
  let testPurchaseOrder: any;
  let testGoodsReceipt: any;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await testSetup.teardown();
  });

  async function setupTestData() {
    // Create test unit
    testUnit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Unit',
        abbreviation: 'TU',
        type: 'COUNT',
        isBaseUnit: true,
      },
    });

    // Create test supplier
    testSupplier = await ctx.prisma.supplier.create({
      data: {
        code: 'TEST-PROC-SUP-001',
        name: 'Test Procurement Supplier',
        type: 'PBF',
        status: 'ACTIVE',
        createdBy: ctx.users.admin.id,
      },
    });

    // Create test product
    testProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-PROC-PROD-001',
        name: 'Test Procurement Product',
        type: 'MEDICINE',
        category: 'ANALGESIC',
        medicineClassification: 'OBAT_KERAS',
        baseUnitId: testUnit.id,
        createdBy: ctx.users.admin.id,
      },
    });
  }

  async function cleanupTestData() {
    try {
      // Delete in reverse order of dependencies
      await ctx.prisma.goodsReceiptItem.deleteMany({});
      await ctx.prisma.goodsReceipt.deleteMany({});
      await ctx.prisma.purchaseOrderItem.deleteMany({});
      await ctx.prisma.purchaseOrder.deleteMany({});
      await ctx.prisma.product.deleteMany({ where: { code: 'TEST-PROC-PROD-001' } });
      await ctx.prisma.supplier.deleteMany({ where: { code: 'TEST-PROC-SUP-001' } });
      await ctx.prisma.productUnit.deleteMany({ where: { name: 'Test Unit' } });
    } catch (error) {
      console.warn('Failed to cleanup test data:', error.message);
    }
  }

  describe('Purchase Orders API (/api/purchase-orders)', () => {
    describe('POST /api/purchase-orders', () => {
      it('should create a new purchase order with valid data (Admin)', async () => {
        const purchaseOrderData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          orderDate: new Date().toISOString(),
          expectedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          paymentTerms: 30,
          paymentMethod: 'TRANSFER',
          deliveryAddress: 'Test Delivery Address',
          deliveryContact: 'Test Contact',
          deliveryPhone: '+62 812 3456 7890',
          notes: 'Test purchase order',
          items: [
            {
              productId: testProduct?.id || 'mock-product-id',
              unitId: testUnit?.id || 'mock-unit-id',
              quantityOrdered: 100,
              unitPrice: 5000,
              expectedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              qualitySpecs: 'Standard quality',
              notes: 'Test item',
            },
          ],
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(purchaseOrderData);

        expectSuccess(response, 201);
        expect(response.body.orderNumber).toMatch(/^PO-\d{8}-\d{3}$/);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.supplierId).toBe(testSupplier.id);
        expect(response.body.items).toHaveLength(1);
        expect(response.body.items[0].quantityOrdered).toBe(100);
        expect(response.body.items[0].unitPrice).toBe('5000');
        testPurchaseOrder = response.body;
      });

      it('should create purchase order with Pharmacist role', async () => {
        const purchaseOrderData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          orderDate: new Date().toISOString(),
          paymentMethod: 'CASH',
          notes: 'Pharmacist created order',
          items: [
            {
              productId: testProduct?.id || 'mock-product-id',
              unitId: testUnit?.id || 'mock-unit-id',
              quantityOrdered: 50,
              unitPrice: 3000,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
          .send(purchaseOrderData);

        if (testSupplier && testProduct) {
          expectSuccess(response, 201);
          expect(response.body.orderNumber).toMatch(/^PO-\d{8}-\d{3}$/);
        } else {
          expect([400, 404]).toContain(response.status);
        }
      });

      it('should reject purchase order creation by Cashier', async () => {
        const purchaseOrderData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          orderDate: new Date().toISOString(),
          items: [
            {
              productId: testProduct?.id || 'mock-product-id',
              unitId: testUnit?.id || 'mock-unit-id',
              quantityOrdered: 25,
              unitPrice: 2000,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(purchaseOrderData);

        expectForbidden(response);
      });

      it('should reject purchase order without authentication', async () => {
        const purchaseOrderData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          items: [],
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .send(purchaseOrderData);

        expectUnauthorized(response);
      });

      it('should validate required fields', async () => {
        const invalidData = {
          // Missing supplierId and items
          orderDate: new Date().toISOString(),
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(invalidData);

        expectValidationError(response);
      });

      it('should validate item data', async () => {
        const invalidItemData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          items: [
            {
              // Missing required fields
              quantityOrdered: -5, // Invalid quantity
            },
          ],
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(invalidItemData);

        expectValidationError(response);
      });

      it('should reject non-existent supplier', async () => {
        const invalidSupplierData = {
          supplierId: 'non-existent-supplier-id',
          items: [
            {
              productId: testProduct?.id || 'mock-product-id',
              unitId: testUnit?.id || 'mock-unit-id',
              quantityOrdered: 10,
              unitPrice: 1000,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(invalidSupplierData);

        expect([400, 404]).toContain(response.status);
        if (response.body.message) {
          expect(response.body.message).toContain('tidak ditemukan');
        }
      });
    });

    describe('GET /api/purchase-orders', () => {
      it('should list purchase orders with pagination', async () => {
        const response = await ctx.request
          .get('/api/purchase-orders?page=1&limit=10')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.data).toBeInstanceOf(Array);
        expect(response.body.meta).toBeDefined();
        expect(response.body.meta.page).toBe(1);
        expect(response.body.meta.limit).toBe(10);
        expect(response.body.meta.total).toBeGreaterThanOrEqual(0);
      });

      it('should filter purchase orders by supplier', async () => {
        if (testSupplier) {
          const response = await ctx.request
            .get(`/api/purchase-orders?supplierId=${testSupplier.id}`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(response);
          expect(response.body.data).toBeInstanceOf(Array);
          // All returned orders should be from the specified supplier
          response.body.data.forEach((order: any) => {
            expect(order.supplierId).toBe(testSupplier.id);
          });
        }
      });

      it('should filter purchase orders by status', async () => {
        const response = await ctx.request
          .get('/api/purchase-orders?status=DRAFT')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.data).toBeInstanceOf(Array);
        // All returned orders should have DRAFT status
        response.body.data.forEach((order: any) => {
          expect(order.status).toBe('DRAFT');
        });
      });

      it('should search purchase orders', async () => {
        const response = await ctx.request
          .get('/api/purchase-orders?search=PO-')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.data).toBeInstanceOf(Array);
      });

      it('should allow Cashier to view purchase orders', async () => {
        const response = await ctx.request
          .get('/api/purchase-orders')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);
        expect(response.body.data).toBeInstanceOf(Array);
      });
    });

    describe('GET /api/purchase-orders/stats', () => {
      it('should return purchase order statistics', async () => {
        const response = await ctx.request
          .get('/api/purchase-orders/stats')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.totalOrders).toBeGreaterThanOrEqual(0);
        expect(response.body.statusStats).toBeDefined();
        expect(response.body.totalValue).toBeGreaterThanOrEqual(0);
        expect(response.body.recentOrders).toBeInstanceOf(Array);
      });
    });

    describe('GET /api/purchase-orders/:id', () => {
      it('should get specific purchase order', async () => {
        if (testPurchaseOrder) {
          const response = await ctx.request
            .get(`/api/purchase-orders/${testPurchaseOrder.id}`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(response);
          expect(response.body.id).toBe(testPurchaseOrder.id);
          expect(response.body.orderNumber).toBe(testPurchaseOrder.orderNumber);
        }
      });

      it('should return 404 for non-existent purchase order', async () => {
        const response = await ctx.request
          .get('/api/purchase-orders/non-existent-id')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectNotFound(response);
      });
    });

    describe('POST /api/purchase-orders/:id/approve', () => {
      it('should approve purchase order (Admin)', async () => {
        if (testPurchaseOrder) {
          // First submit the order
          const statusUpdateResponse = await ctx.request
            .patch(`/api/purchase-orders/${testPurchaseOrder.id}/status`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send({ status: 'SUBMITTED' });

          expectSuccess(statusUpdateResponse);

          const approvalData = {
            approvalNotes: 'Approved for procurement',
          };

          const response = await ctx.request
            .post(`/api/purchase-orders/${testPurchaseOrder.id}/approve`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(approvalData);

          expectSuccess(response, 201);
          expect(response.body.status).toBe('APPROVED');
        }
      });

      it('should reject approval by Cashier', async () => {
        if (testPurchaseOrder) {
          const response = await ctx.request
            .post(`/api/purchase-orders/${testPurchaseOrder.id}/approve`)
            .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
            .send({});

          expectForbidden(response);
        }
      });
    });

    describe('POST /api/purchase-orders/:id/cancel', () => {
      it('should cancel purchase order with reason', async () => {
        if (testSupplier && testProduct) {
          // Create a new purchase order for cancellation
          const purchaseOrderData = {
            supplierId: testSupplier.id,
            orderDate: new Date().toISOString(),
            paymentMethod: 'CASH',
            notes: 'Order to be cancelled',
            items: [
              {
                productId: testProduct.id,
                unitId: testUnit.id,
                quantityOrdered: 25,
                unitPrice: 2000,
              },
            ],
          };

          const createResponse = await ctx.request
            .post('/api/purchase-orders')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(purchaseOrderData);

          if (createResponse.status === 201) {
            const response = await ctx.request
              .post(`/api/purchase-orders/${createResponse.body.id}/cancel`)
              .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
              .send({ reason: 'Supplier tidak tersedia' });

            expectSuccess(response, 201);
            expect(response.body.status).toBe('CANCELLED');
          }
        }
      });

      it('should reject cancellation without reason', async () => {
        if (testPurchaseOrder) {
          const response = await ctx.request
            .post(`/api/purchase-orders/${testPurchaseOrder.id}/cancel`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send({});

          expectValidationError(response);
        }
      });
    });
  });

  describe('Goods Receipts API (/api/goods-receipts)', () => {
    describe('POST /api/goods-receipts', () => {
      it('should create goods receipt with valid data (Admin)', async () => {
        const goodsReceiptData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          purchaseOrderId: testPurchaseOrder?.id,
          receiptDate: new Date().toISOString(),
          deliveryDate: new Date().toISOString(),
          invoiceNumber: 'INV-TEST-001',
          deliveryNote: 'DN-TEST-001',
          deliveredBy: 'Test Delivery Person',
          notes: 'Test goods receipt',
          items: [
            {
              productId: testProduct?.id || 'mock-product-id',
              unitId: testUnit?.id || 'mock-unit-id',
              quantityOrdered: 100,
              quantityReceived: 95,
              unitPrice: 5000,
              batchNumber: 'BATCH-001',
              expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
              manufacturingDate: new Date().toISOString(),
              storageLocation: 'Rak A-1',
              conditionOnReceipt: 'GOOD',
              notes: 'Test receipt item',
            },
          ],
        };

        const response = await ctx.request
          .post('/api/goods-receipts')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(goodsReceiptData);

        if (testSupplier && testProduct) {
          expectSuccess(response, 201);
          expect(response.body.receiptNumber).toMatch(/^GR-\d{8}-\d{3}$/);
          expect(response.body.status).toBe('PENDING');
          expect(response.body.supplierId).toBe(testSupplier.id);
          expect(response.body.items).toHaveLength(1);
          testGoodsReceipt = response.body;
        } else {
          expect([400, 404]).toContain(response.status);
        }
      });

      it('should reject goods receipt creation by Cashier', async () => {
        const goodsReceiptData = {
          supplierId: testSupplier?.id || 'mock-supplier-id',
          items: [],
        };

        const response = await ctx.request
          .post('/api/goods-receipts')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(goodsReceiptData);

        expectForbidden(response);
      });
    });

    describe('GET /api/goods-receipts', () => {
      it('should list goods receipts with pagination', async () => {
        const response = await ctx.request
          .get('/api/goods-receipts?page=1&limit=10')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.data).toBeInstanceOf(Array);
        expect(response.body.meta).toBeDefined();
      });

      it('should filter goods receipts by status', async () => {
        const response = await ctx.request
          .get('/api/goods-receipts?status=PENDING')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.data).toBeInstanceOf(Array);
      });
    });

    describe('POST /api/goods-receipts/:id/quality-control', () => {
      it('should update quality control status', async () => {
        if (testGoodsReceipt) {
          const qualityData = {
            qualityStatus: 'PASSED',
            qualityNotes: 'Quality check passed',
            temperatureCheck: true,
            packagingCheck: true,
            documentationCheck: true,
            bpomCheck: true,
          };

          const response = await ctx.request
            .post(`/api/goods-receipts/${testGoodsReceipt.id}/quality-control`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(qualityData);

          expectSuccess(response, 201);
          expect(response.body.qualityStatus).toBe('PASSED');
        }
      });
    });

    describe('POST /api/goods-receipts/:id/approve', () => {
      it('should approve goods receipt', async () => {
        if (testGoodsReceipt) {
          const response = await ctx.request
            .post(`/api/goods-receipts/${testGoodsReceipt.id}/approve`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(response, 201);
          expect(response.body.status).toBe('APPROVED');
        }
      });
    });

    describe('GET /api/goods-receipts/stats', () => {
      it('should return goods receipt statistics', async () => {
        const response = await ctx.request
          .get('/api/goods-receipts/stats')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);

        // Test existing fields
        expect(response.body.totalReceipts).toBeGreaterThanOrEqual(0);
        expect(response.body.statusStats).toBeDefined();
        expect(response.body.qualityStats).toBeDefined();
        expect(response.body.totalValue).toBeGreaterThanOrEqual(0);
        expect(response.body.recentReceipts).toBeInstanceOf(Array);

        // Test new fields added for frontend compatibility
        expect(response.body.pendingInspection).toBeGreaterThanOrEqual(0);
        expect(response.body.approved).toBeGreaterThanOrEqual(0);
        expect(response.body.rejected).toBeGreaterThanOrEqual(0);
        expect(response.body.qualityPassRate).toBeGreaterThanOrEqual(0);
        expect(response.body.qualityPassRate).toBeLessThanOrEqual(1);
        expect(response.body.averageInspectionTime).toBeGreaterThanOrEqual(0);

        // Test data types
        expect(typeof response.body.totalReceipts).toBe('number');
        expect(typeof response.body.pendingInspection).toBe('number');
        expect(typeof response.body.approved).toBe('number');
        expect(typeof response.body.rejected).toBe('number');
        expect(typeof response.body.qualityPassRate).toBe('number');
        expect(typeof response.body.averageInspectionTime).toBe('number');
        expect(typeof response.body.totalValue).toBe('number');
      });
    });
  });
});
