import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../../src/prisma/prisma.service';
import { NumberGeneratorService } from '../../src/procurement/utils/number-generator.utils';
import { ProcurementValidationUtils } from '../../src/procurement/utils/procurement-validation.utils';
import {
  PurchaseOrderStatus,
  GoodsReceiptStatus,
  QualityControlStatus,
  PurchaseOrderItemStatus,
  PaymentMethod,
  SupplierType,
  SupplierStatus,
  ProductType,
  ProductCategory,
  MedicineClassification,
  UnitType,
  UserRole
} from '@prisma/client';

describe('Procurement Models Integration', () => {
  let prisma: PrismaService;
  let numberGenerator: NumberGeneratorService;
  let testSupplier: any;
  let testProduct: any;
  let testUnit: any;
  let testUser: any;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PrismaService, NumberGeneratorService],
    }).compile();

    prisma = module.get<PrismaService>(PrismaService);
    numberGenerator = module.get<NumberGeneratorService>(NumberGeneratorService);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await prisma.$disconnect();
  });

  async function setupTestData() {
    // Create test user
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.ADMIN,
      },
    });

    // Create test unit
    testUnit = await prisma.productUnit.create({
      data: {
        name: 'Tablet',
        abbreviation: 'tab',
        type: UnitType.COUNT,
        isBaseUnit: true,
      },
    });

    // Create test supplier
    testSupplier = await prisma.supplier.create({
      data: {
        code: 'TEST-SUPPLIER-001',
        name: 'Test Pharmaceutical Supplier',
        type: SupplierType.PBF,
        status: SupplierStatus.ACTIVE,
        createdBy: testUser.id,
      },
    });

    // Create test product
    testProduct = await prisma.product.create({
      data: {
        code: 'TEST-PRODUCT-001',
        name: 'Test Medicine',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_KERAS,
        baseUnitId: testUnit.id,
        createdBy: testUser.id,
      },
    });
  }

  async function cleanupTestData() {
    // Delete in reverse order of dependencies
    await prisma.goodsReceiptItem.deleteMany({});
    await prisma.goodsReceipt.deleteMany({});
    await prisma.purchaseOrderItem.deleteMany({});
    await prisma.purchaseOrder.deleteMany({});
    await prisma.product.deleteMany({ where: { code: 'TEST-PRODUCT-001' } });
    await prisma.supplier.deleteMany({ where: { code: 'TEST-SUPPLIER-001' } });
    await prisma.productUnit.deleteMany({ where: { name: 'Tablet' } });
    await prisma.user.deleteMany({ where: { email: '<EMAIL>' } });
  }

  describe('Purchase Order Creation', () => {
    it('should create a purchase order with items', async () => {
      const orderNumber = await numberGenerator.generatePurchaseOrderNumber();

      const purchaseOrder = await prisma.purchaseOrder.create({
        data: {
          orderNumber,
          supplierId: testSupplier.id,
          status: PurchaseOrderStatus.DRAFT,
          subtotal: 100000,
          totalAmount: 110000,
          taxAmount: 10000,
          paymentMethod: PaymentMethod.TRANSFER,
          createdBy: testUser.id,
          items: {
            create: [
              {
                productId: testProduct.id,
                unitId: testUnit.id,
                quantityOrdered: 100,
                unitPrice: 1000,
                totalPrice: 100000,
                status: PurchaseOrderItemStatus.PENDING,
              },
            ],
          },
        },
        include: {
          items: true,
          supplier: true,
        },
      });

      expect(purchaseOrder).toBeDefined();
      expect(purchaseOrder.orderNumber).toMatch(/^PO-\d{8}-\d{3}$/);
      expect(purchaseOrder.status).toBe(PurchaseOrderStatus.DRAFT);
      expect(purchaseOrder.items).toHaveLength(1);
      expect(purchaseOrder.items[0].quantityOrdered).toBe(100);
      expect(purchaseOrder.supplier.name).toBe('Test Pharmaceutical Supplier');
    });

    it('should enforce unique order numbers', async () => {
      const orderNumber = await numberGenerator.generatePurchaseOrderNumber();

      // Create first order
      await prisma.purchaseOrder.create({
        data: {
          orderNumber,
          supplierId: testSupplier.id,
          createdBy: testUser.id,
        },
      });

      // Try to create second order with same number
      await expect(
        prisma.purchaseOrder.create({
          data: {
            orderNumber,
            supplierId: testSupplier.id,
            createdBy: testUser.id,
          },
        })
      ).rejects.toThrow();
    });
  });

  describe('Goods Receipt Creation', () => {
    let testPurchaseOrder: any;

    beforeEach(async () => {
      const orderNumber = await numberGenerator.generatePurchaseOrderNumber();
      testPurchaseOrder = await prisma.purchaseOrder.create({
        data: {
          orderNumber,
          supplierId: testSupplier.id,
          status: PurchaseOrderStatus.ORDERED,
          createdBy: testUser.id,
          items: {
            create: [
              {
                productId: testProduct.id,
                unitId: testUnit.id,
                quantityOrdered: 100,
                unitPrice: 1000,
                totalPrice: 100000,
                status: PurchaseOrderItemStatus.ORDERED,
              },
            ],
          },
        },
        include: { items: true },
      });
    });

    it('should create a goods receipt with items', async () => {
      const receiptNumber = await numberGenerator.generateGoodsReceiptNumber();

      const goodsReceipt = await prisma.goodsReceipt.create({
        data: {
          receiptNumber,
          purchaseOrderId: testPurchaseOrder.id,
          supplierId: testSupplier.id,
          status: GoodsReceiptStatus.PENDING,
          qualityStatus: QualityControlStatus.PENDING,
          totalAmount: 100000,
          createdBy: testUser.id,
          items: {
            create: [
              {
                purchaseOrderItemId: testPurchaseOrder.items[0].id,
                productId: testProduct.id,
                unitId: testUnit.id,
                quantityOrdered: 100,
                quantityReceived: 95,
                unitPrice: 1000,
                totalPrice: 95000,
                batchNumber: 'BATCH-001',
                expiryDate: new Date('2025-12-31'),
                qualityStatus: QualityControlStatus.PENDING,
              },
            ],
          },
        },
        include: {
          items: true,
          purchaseOrder: true,
        },
      });

      expect(goodsReceipt).toBeDefined();
      expect(goodsReceipt.receiptNumber).toMatch(/^GR-\d{8}-\d{3}$/);
      expect(goodsReceipt.status).toBe(GoodsReceiptStatus.PENDING);
      expect(goodsReceipt.items).toHaveLength(1);
      expect(goodsReceipt.items[0].quantityReceived).toBe(95);
      expect(goodsReceipt.items[0].batchNumber).toBe('BATCH-001');
    });

    it('should allow goods receipt without purchase order', async () => {
      const receiptNumber = await numberGenerator.generateGoodsReceiptNumber();

      const goodsReceipt = await prisma.goodsReceipt.create({
        data: {
          receiptNumber,
          supplierId: testSupplier.id,
          status: GoodsReceiptStatus.PENDING,
          qualityStatus: QualityControlStatus.PENDING,
          totalAmount: 50000,
          createdBy: testUser.id,
          items: {
            create: [
              {
                productId: testProduct.id,
                unitId: testUnit.id,
                quantityReceived: 50,
                unitPrice: 1000,
                totalPrice: 50000,
                batchNumber: 'BATCH-002',
                expiryDate: new Date('2025-12-31'),
                qualityStatus: QualityControlStatus.PENDING,
              },
            ],
          },
        },
        include: { items: true },
      });

      expect(goodsReceipt).toBeDefined();
      expect(goodsReceipt.purchaseOrderId).toBeNull();
      expect(goodsReceipt.items[0].purchaseOrderItemId).toBeNull();
    });
  });

  describe('Business Logic Validation', () => {
    it('should validate purchase order status transitions', () => {
      expect(
        ProcurementValidationUtils.validatePurchaseOrderStatusTransition(
          PurchaseOrderStatus.DRAFT,
          PurchaseOrderStatus.SUBMITTED
        )
      ).toBe(true);

      expect(
        ProcurementValidationUtils.validatePurchaseOrderStatusTransition(
          PurchaseOrderStatus.COMPLETED,
          PurchaseOrderStatus.DRAFT
        )
      ).toBe(false);
    });

    it('should validate goods receipt status transitions', () => {
      expect(
        ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
          GoodsReceiptStatus.PENDING,
          GoodsReceiptStatus.IN_INSPECTION
        )
      ).toBe(true);

      expect(
        ProcurementValidationUtils.validateGoodsReceiptStatusTransition(
          GoodsReceiptStatus.COMPLETED,
          GoodsReceiptStatus.PENDING
        )
      ).toBe(false);
    });

    it('should validate expiry dates', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 2);

      expect(() => {
        ProcurementValidationUtils.validateExpiryDate(futureDate);
      }).not.toThrow();

      const pastDate = new Date();
      pastDate.setFullYear(pastDate.getFullYear() - 1);

      expect(() => {
        ProcurementValidationUtils.validateExpiryDate(pastDate);
      }).toThrow('Tanggal kedaluwarsa tidak boleh di masa lalu');
    });

    it('should validate batch numbers', () => {
      expect(ProcurementValidationUtils.validateBatchNumber('BATCH001')).toBe(true);
      expect(ProcurementValidationUtils.validateBatchNumber('B-123')).toBe(true);
      expect(ProcurementValidationUtils.validateBatchNumber('')).toBe(false);
      expect(ProcurementValidationUtils.validateBatchNumber('AB')).toBe(false); // Too short
    });
  });

  describe('Number Generation', () => {
    it('should generate unique purchase order numbers', async () => {
      const number1 = await numberGenerator.generatePurchaseOrderNumber();

      // Create a purchase order with the first number to ensure it's used
      await prisma.purchaseOrder.create({
        data: {
          orderNumber: number1,
          supplierId: testSupplier.id,
          createdBy: testUser.id,
        },
      });

      const number2 = await numberGenerator.generatePurchaseOrderNumber();

      expect(number1).not.toBe(number2);
      expect(NumberGeneratorService.validateNumberFormat(number1, 'PO')).toBe(true);
      expect(NumberGeneratorService.validateNumberFormat(number2, 'PO')).toBe(true);
    });

    it('should generate unique goods receipt numbers', async () => {
      const number1 = await numberGenerator.generateGoodsReceiptNumber();

      // Create a goods receipt with the first number to ensure it's used
      await prisma.goodsReceipt.create({
        data: {
          receiptNumber: number1,
          supplierId: testSupplier.id,
          createdBy: testUser.id,
        },
      });

      const number2 = await numberGenerator.generateGoodsReceiptNumber();

      expect(number1).not.toBe(number2);
      expect(NumberGeneratorService.validateNumberFormat(number1, 'GR')).toBe(true);
      expect(NumberGeneratorService.validateNumberFormat(number2, 'GR')).toBe(true);
    });

    it('should extract date from generated numbers', () => {
      const testNumber = 'PO-20240615-001';
      const extractedDate = NumberGeneratorService.extractDateFromNumber(testNumber);

      expect(extractedDate).toBeInstanceOf(Date);
      expect(extractedDate?.getFullYear()).toBe(2024);
      expect(extractedDate?.getMonth()).toBe(5); // June (0-indexed)
      expect(extractedDate?.getDate()).toBe(15);
    });
  });
});
